{"name": "ai-bi-backend", "version": "1.0.0", "description": "AI-BI System Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "npx prisma migrate dev", "migrate:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "keywords": ["api", "backend", "ai", "bi"], "author": "AI-BI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "@prisma/client": "^5.7.1", "redis": "^4.6.12", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "pg": "^8.11.3", "mysql2": "^3.6.5", "openai": "^4.20.1", "axios": "^1.6.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/pg": "^8.10.9", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "prisma": "^5.7.1"}}