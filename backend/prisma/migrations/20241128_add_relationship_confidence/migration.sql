-- 为数据关联关系表添加置信度相关字段

-- 添加发现方法字段
ALTER TABLE "data_relationships" 
ADD COLUMN "discovery_method" VARCHAR(50);

-- 添加置信度等级字段
ALTER TABLE "data_relationships" 
ADD COLUMN "confidence" VARCHAR(20);

-- 添加置信度分数字段
ALTER TABLE "data_relationships" 
ADD COLUMN "confidence_score" REAL;

-- 添加证据字段（文本数组）
ALTER TABLE "data_relationships" 
ADD COLUMN "evidence" TEXT[];

-- 添加元数据字段（JSON）
ALTER TABLE "data_relationships" 
ADD COLUMN "metadata" JSONB;

-- 为现有记录设置默认值
UPDATE "data_relationships" 
SET 
  "discovery_method" = CASE 
    WHEN "is_manual" = true THEN 'manual'
    ELSE 'naming_convention'
  END,
  "confidence" = CASE 
    WHEN "is_manual" = true THEN 'high'
    ELSE 'medium'
  END,
  "confidence_score" = CASE 
    WHEN "is_manual" = true THEN 0.95
    ELSE 0.6
  END,
  "evidence" = CASE 
    WHEN "is_manual" = true THEN ARRAY['用户手动创建']
    ELSE ARRAY['命名约定匹配']
  END
WHERE "discovery_method" IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX "idx_data_relationships_confidence_score" 
ON "data_relationships" ("confidence_score" DESC);

CREATE INDEX "idx_data_relationships_discovery_method" 
ON "data_relationships" ("discovery_method");

CREATE INDEX "idx_data_relationships_confidence" 
ON "data_relationships" ("confidence");

-- 添加约束确保置信度分数在有效范围内
ALTER TABLE "data_relationships" 
ADD CONSTRAINT "chk_confidence_score_range" 
CHECK ("confidence_score" IS NULL OR ("confidence_score" >= 0 AND "confidence_score" <= 1));

-- 添加约束确保置信度等级的有效值
ALTER TABLE "data_relationships" 
ADD CONSTRAINT "chk_confidence_values" 
CHECK ("confidence" IS NULL OR "confidence" IN ('high', 'medium', 'low'));

-- 添加约束确保发现方法的有效值
ALTER TABLE "data_relationships" 
ADD CONSTRAINT "chk_discovery_method_values" 
CHECK ("discovery_method" IS NULL OR "discovery_method" IN ('foreign_key', 'naming_convention', 'data_analysis', 'manual'));
