// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 数据源表
model DataSource {
  id               String   @id @default(uuid()) @db.Uuid
  name             String   @db.VarChar(255)
  type             String   @db.VarChar(50) // postgresql, mysql, csv, excel
  connectionConfig String   @db.Text // 加密后的连接配置信息 (JSON 格式)
  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt        DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关联关系
  dataTables DataTable[]

  @@map("data_sources")
}

// 数据表元数据表
model DataTable {
  id                String   @id @default(uuid()) @db.Uuid
  dataSourceId      String   @map("data_source_id") @db.Uuid
  originalTableName String   @map("original_table_name") @db.VarChar(255)
  aliasName         String?  @map("alias_name") @db.VarChar(255)
  description       String?  @db.Text
  isSynced          Boolean  @default(false) @map("is_synced")
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt         DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关联关系
  dataSource  DataSource   @relation(fields: [dataSourceId], references: [id], onDelete: Cascade)
  dataColumns DataColumn[]

  @@map("data_tables")
}

// 数据列元数据表
model DataColumn {
  id                 String   @id @default(uuid()) @db.Uuid
  dataTableId        String   @map("data_table_id") @db.Uuid
  originalColumnName String   @map("original_column_name") @db.VarChar(255)
  originalDataType   String   @map("original_data_type") @db.VarChar(100)
  aliasName          String?  @map("alias_name") @db.VarChar(255)
  description        String?  @db.Text
  isPrimaryKey       Boolean  @default(false) @map("is_primary_key")
  createdAt          DateTime @default(now()) @map("created_at") @db.Timestamptz
  updatedAt          DateTime @updatedAt @map("updated_at") @db.Timestamptz

  // 关联关系
  dataTable DataTable @relation(fields: [dataTableId], references: [id], onDelete: Cascade)

  // 作为关联关系的起始列
  fromRelationships DataRelationship[] @relation("FromColumn")
  // 作为关联关系的目标列
  toRelationships   DataRelationship[] @relation("ToColumn")

  @@map("data_columns")
}

// 数据关联关系表
model DataRelationship {
  id               String   @id @default(uuid()) @db.Uuid
  fromColumnId     String   @map("from_column_id") @db.Uuid
  toColumnId       String   @map("to_column_id") @db.Uuid
  relationshipType String   @map("relationship_type") @db.VarChar(50) // one_to_one, one_to_many, many_to_one
  isManual         Boolean  @default(false) @map("is_manual") // 是否为用户手动创建

  // 置信度相关字段
  discoveryMethod  String?  @map("discovery_method") @db.VarChar(50) // foreign_key, naming_convention, data_analysis, manual
  confidence       String?  @map("confidence") @db.VarChar(20) // high, medium, low
  confidenceScore  Float?   @map("confidence_score") @db.Real // 0-1之间的数值
  evidence         String[] @map("evidence") // 支持该关联关系的证据
  metadata         Json?    @map("metadata") // 额外的元数据信息

  createdAt        DateTime @default(now()) @map("created_at") @db.Timestamptz

  // 关联关系
  fromColumn DataColumn @relation("FromColumn", fields: [fromColumnId], references: [id], onDelete: Cascade)
  toColumn   DataColumn @relation("ToColumn", fields: [toColumnId], references: [id], onDelete: Cascade)

  @@map("data_relationships")
}

// 对话历史记录表
model ChatHistory {
  id                       String   @id @default(uuid()) @db.Uuid
  sessionId                String   @map("session_id") @db.VarChar(255)
  userQuery                String   @map("user_query") @db.Text
  generatedSql             String?  @map("generated_sql") @db.Text
  queryResultData          Json?    @map("query_result_data") @db.JsonB
  naturalLanguageResponse  String?  @map("natural_language_response") @db.Text
  visualizationSpec        Json?    @map("visualization_spec") @db.JsonB
  createdAt                DateTime @default(now()) @map("created_at") @db.Timestamptz

  @@map("chat_history")
}
