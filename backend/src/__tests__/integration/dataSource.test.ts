import request from 'supertest';
import express from 'express';
import { PrismaClient } from '@prisma/client';
import dataSourceRoutes from '../../routes/dataSourceRoutes';

// 创建测试应用
const app = express();
app.use(express.json());
app.use('/api/data-sources', dataSourceRoutes);

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  res.status(err.status || 500).json({
    success: false,
    error: err.message || 'Internal Server Error'
  });
});

describe('Data Source API Integration Tests', () => {
  let prisma: PrismaClient;

  beforeEach(() => {
    prisma = new PrismaClient();
    jest.clearAllMocks();
  });

  describe('GET /api/data-sources', () => {
    it('should return list of data sources', async () => {
      const mockDataSources = [
        {
          id: '1',
          name: 'Test PostgreSQL',
          type: 'postgresql',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      (prisma.dataSource.findMany as jest.Mock).mockResolvedValue(mockDataSources);

      const response = await request(app)
        .get('/api/data-sources')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.arrayContaining([
          expect.objectContaining({
            id: '1',
            name: 'Test PostgreSQL',
            type: 'postgresql',
          }),
        ]),
      });
    });

    it('should handle database errors', async () => {
      (prisma.dataSource.findMany as jest.Mock).mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/data-sources')
        .expect(500);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('Database connection failed'),
      });
    });
  });

  describe('POST /api/data-sources', () => {
    it('should create a new data source', async () => {
      const newDataSource = {
        name: 'New PostgreSQL DB',
        type: 'postgresql',
        connectionConfig: {
          host: 'localhost',
          port: 5432,
          database: 'test_db',
          username: 'test_user',
          password: 'test_password',
        },
      };

      const mockCreatedDataSource = {
        id: 'new-id',
        ...newDataSource,
        connectionConfig: 'encrypted-config',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (prisma.dataSource.create as jest.Mock).mockResolvedValue(mockCreatedDataSource);

      const response = await request(app)
        .post('/api/data-sources')
        .send(newDataSource)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        data: { id: 'new-id' },
        message: '数据源创建成功',
      });
    });

    it('should validate required fields', async () => {
      const invalidDataSource = {
        name: '', // 空名称
        type: 'postgresql',
        connectionConfig: {},
      };

      const response = await request(app)
        .post('/api/data-sources')
        .send(invalidDataSource)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('数据源名称不能为空'),
      });
    });

    it('should validate data source type', async () => {
      const invalidDataSource = {
        name: 'Test DB',
        type: 'invalid-type',
        connectionConfig: {},
      };

      const response = await request(app)
        .post('/api/data-sources')
        .send(invalidDataSource)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('数据源类型必须是'),
      });
    });
  });

  describe('GET /api/data-sources/:id', () => {
    it('should return data source details', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
        dataTables: [
          {
            id: 'table-1',
            originalTableName: 'users',
            aliasName: '用户表',
            dataColumns: [
              {
                id: 'column-1',
                originalColumnName: 'id',
                originalDataType: 'integer',
                aliasName: '用户ID',
                isPrimaryKey: true,
              },
            ],
          },
        ],
      };

      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(mockDataSource);
      (prisma.dataRelationship.findMany as jest.Mock).mockResolvedValue([]);

      const response = await request(app)
        .get('/api/data-sources/1')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          id: '1',
          name: 'Test DB',
          tables: expect.arrayContaining([
            expect.objectContaining({
              id: 'table-1',
              originalName: 'users',
              aliasName: '用户表',
            }),
          ]),
        }),
      });
    });

    it('should return 404 for non-existent data source', async () => {
      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .get('/api/data-sources/non-existent')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: '数据源不存在',
      });
    });
  });

  describe('DELETE /api/data-sources/:id', () => {
    it('should delete existing data source', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
      };

      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(mockDataSource);
      (prisma.dataSource.delete as jest.Mock).mockResolvedValue(mockDataSource);

      const response = await request(app)
        .delete('/api/data-sources/1')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: '数据源删除成功',
      });
    });

    it('should return 404 for non-existent data source', async () => {
      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await request(app)
        .delete('/api/data-sources/non-existent')
        .expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: '数据源不存在',
      });
    });
  });

  describe('POST /api/data-sources/test-connection', () => {
    it('should test connection successfully', async () => {
      const connectionConfig = {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      // 这里需要mock DataSourceService的testConnection方法
      // 由于我们在集成测试中，可能需要更复杂的mock设置

      const response = await request(app)
        .post('/api/data-sources/test-connection')
        .send({
          type: 'postgresql',
          connectionConfig,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: { connected: expect.any(Boolean) },
      });
    });

    it('should handle connection test failure', async () => {
      const connectionConfig = {
        host: 'invalid-host',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const response = await request(app)
        .post('/api/data-sources/test-connection')
        .send({
          type: 'postgresql',
          connectionConfig,
        })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('连接测试失败'),
      });
    });
  });
});
