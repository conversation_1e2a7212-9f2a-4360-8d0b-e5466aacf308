import { PrismaClient } from '@prisma/client';
import { ChatService } from '../../services/chatService';
import { ChatRequest } from '../../types';

describe('ChatService', () => {
  let prisma: PrismaClient;
  let chatService: ChatService;

  beforeEach(() => {
    prisma = new PrismaClient();
    chatService = new ChatService(prisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processQuery', () => {
    it('should process a natural language query successfully', async () => {
      const request: ChatRequest = {
        sessionId: 'test-session',
        prompt: '最近一个月的销售总额是多少？',
      };

      const mockSemanticContext = '# 数据模型\n## 表: orders\n- amount (decimal)';
      const mockQueryResult = {
        columns: ['总销售额'],
        rows: [[1000000]],
      };

      // Mock semantic service
      jest.spyOn(chatService['semanticService'], 'getSemanticContext').mockResolvedValue(mockSemanticContext);
      
      // Mock AI service
      jest.spyOn(chatService['aiService'], 'generateSQL').mockResolvedValue({
        sql: 'SELECT SUM(amount) as total_amount FROM orders WHERE created_at >= CURRENT_DATE - INTERVAL \'30 days\'',
        explanation: '查询最近30天的订单总金额',
      });
      
      jest.spyOn(chatService['aiService'], 'generateNaturalLanguageResponse').mockResolvedValue(
        '最近一个月的总销售额为 1,000,000 元。'
      );

      // Mock executeQuery
      jest.spyOn(chatService as any, 'executeQuery').mockResolvedValue(mockQueryResult);

      // Mock saveChatHistory
      const mockChatHistory = {
        id: 'chat-1',
        sessionId: 'test-session',
        userQuery: request.prompt,
        naturalLanguageResponse: '最近一个月的总销售额为 1,000,000 元。',
      };
      jest.spyOn(chatService as any, 'saveChatHistory').mockResolvedValue(mockChatHistory);

      const result = await chatService.processQuery(request);

      expect(result).toMatchObject({
        id: 'chat-1',
        sessionId: 'test-session',
        userPrompt: '最近一个月的销售总额是多少？',
        responseText: '最近一个月的总销售额为 1,000,000 元。',
        data: mockQueryResult,
      });
    });

    it('should handle errors gracefully', async () => {
      const request: ChatRequest = {
        sessionId: 'test-session',
        prompt: '无效查询',
      };

      jest.spyOn(chatService['semanticService'], 'getSemanticContext').mockRejectedValue(
        new Error('获取语义上下文失败')
      );

      await expect(chatService.processQuery(request)).rejects.toThrow('查询处理失败');
    });
  });

  describe('getChatHistory', () => {
    it('should return chat history for a session', async () => {
      const mockChatHistory = [
        {
          id: 'chat-1',
          sessionId: 'test-session',
          userQuery: '测试问题',
          naturalLanguageResponse: '测试回答',
          queryResultData: { columns: ['test'], rows: [['data']] },
          visualizationSpec: null,
          generatedSql: 'SELECT * FROM test',
          createdAt: new Date(),
        },
      ];

      (prisma.chatHistory.findMany as jest.Mock).mockResolvedValue(mockChatHistory);

      const result = await chatService.getChatHistory('test-session');

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'chat-1',
        sessionId: 'test-session',
        userPrompt: '测试问题',
        responseText: '测试回答',
      });
    });

    it('should return empty array for non-existent session', async () => {
      (prisma.chatHistory.findMany as jest.Mock).mockResolvedValue([]);

      const result = await chatService.getChatHistory('non-existent-session');

      expect(result).toHaveLength(0);
    });

    it('should limit results based on limit parameter', async () => {
      const mockChatHistory = Array.from({ length: 100 }, (_, i) => ({
        id: `chat-${i}`,
        sessionId: 'test-session',
        userQuery: `问题 ${i}`,
        naturalLanguageResponse: `回答 ${i}`,
        queryResultData: null,
        visualizationSpec: null,
        generatedSql: null,
        createdAt: new Date(),
      }));

      (prisma.chatHistory.findMany as jest.Mock).mockResolvedValue(mockChatHistory.slice(0, 10));

      const result = await chatService.getChatHistory('test-session', 10);

      expect(result).toHaveLength(10);
      expect(prisma.chatHistory.findMany).toHaveBeenCalledWith({
        where: { sessionId: 'test-session' },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });
    });
  });

  describe('generateVisualization', () => {
    it('should generate bar chart for comparison queries', async () => {
      const userQuery = '各产品类别的销售额对比';
      const queryResult = {
        columns: ['产品类别', '销售额'],
        rows: [['电子产品', 1000000], ['服装', 800000]],
      };

      jest.spyOn(chatService['aiService'], 'recommendVisualization').mockResolvedValue('bar_chart');

      const result = await (chatService as any).generateVisualization(userQuery, queryResult);

      expect(result).toMatchObject({
        type: 'bar_chart',
        title: '柱状图',
        spec: {
          data: { values: expect.any(Array) },
          mark: 'bar',
        },
      });
    });

    it('should generate pie chart for distribution queries', async () => {
      const userQuery = '各地区销售占比分布';
      const queryResult = {
        columns: ['地区', '占比'],
        rows: [['华东', 0.4], ['华南', 0.3], ['华北', 0.3]],
      };

      jest.spyOn(chatService['aiService'], 'recommendVisualization').mockResolvedValue('pie_chart');

      const result = await (chatService as any).generateVisualization(userQuery, queryResult);

      expect(result).toMatchObject({
        type: 'pie_chart',
        title: '分布图',
        spec: {
          data: { values: expect.any(Array) },
          mark: 'arc',
        },
      });
    });

    it('should fallback to default visualization on AI service failure', async () => {
      const userQuery = '测试查询';
      const queryResult = {
        columns: ['列1', '列2'],
        rows: [['值1', 100]],
      };

      jest.spyOn(chatService['aiService'], 'recommendVisualization').mockRejectedValue(
        new Error('AI服务不可用')
      );

      const result = await (chatService as any).generateVisualization(userQuery, queryResult);

      expect(result).toMatchObject({
        type: 'bar_chart',
        title: '柱状图',
      });
    });
  });
});
