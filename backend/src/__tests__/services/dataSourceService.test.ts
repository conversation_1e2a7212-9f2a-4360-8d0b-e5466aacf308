import { PrismaClient } from '@prisma/client';
import { DataSourceService } from '../../services/dataSourceService';
import { DataSourceType } from '../../types';

describe('DataSourceService', () => {
  let prisma: PrismaClient;
  let dataSourceService: DataSourceService;

  beforeEach(() => {
    prisma = new PrismaClient();
    dataSourceService = new DataSourceService(prisma);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDataSources', () => {
    it('should return list of data sources', async () => {
      const mockDataSources = [
        {
          id: '1',
          name: 'Test DB',
          type: 'postgresql',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      (prisma.dataSource.findMany as jest.Mock).mockResolvedValue(mockDataSources);
      
      // Mock checkConnectionStatus
      jest.spyOn(dataSourceService, 'checkConnectionStatus').mockResolvedValue('connected');

      const result = await dataSourceService.getDataSources();

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
        status: 'connected',
      });
    });

    it('should handle empty result', async () => {
      (prisma.dataSource.findMany as jest.Mock).mockResolvedValue([]);

      const result = await dataSourceService.getDataSources();

      expect(result).toHaveLength(0);
    });
  });

  describe('createDataSource', () => {
    it('should create a new data source', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
        connectionConfig: 'encrypted-config',
      };

      (prisma.dataSource.create as jest.Mock).mockResolvedValue(mockDataSource);
      
      // Mock testConnection and syncMetadata
      jest.spyOn(dataSourceService, 'testConnection').mockResolvedValue(true);
      jest.spyOn(dataSourceService, 'syncMetadata').mockResolvedValue();

      const connectionConfig = {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const result = await dataSourceService.createDataSource(
        'Test DB',
        'postgresql' as DataSourceType,
        connectionConfig
      );

      expect(result).toBe('1');
      expect(prisma.dataSource.create).toHaveBeenCalledWith({
        data: {
          name: 'Test DB',
          type: 'postgresql',
          connectionConfig: expect.any(String),
        },
      });
    });

    it('should throw error if connection test fails', async () => {
      jest.spyOn(dataSourceService, 'testConnection').mockRejectedValue(new Error('Connection failed'));

      const connectionConfig = {
        host: 'invalid-host',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      await expect(
        dataSourceService.createDataSource('Test DB', 'postgresql' as DataSourceType, connectionConfig)
      ).rejects.toThrow('Connection failed');
    });
  });

  describe('deleteDataSource', () => {
    it('should delete existing data source', async () => {
      const mockDataSource = {
        id: '1',
        name: 'Test DB',
        type: 'postgresql',
      };

      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(mockDataSource);
      (prisma.dataSource.delete as jest.Mock).mockResolvedValue(mockDataSource);

      await dataSourceService.deleteDataSource('1');

      expect(prisma.dataSource.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      });
    });

    it('should throw error if data source not found', async () => {
      (prisma.dataSource.findUnique as jest.Mock).mockResolvedValue(null);

      await expect(dataSourceService.deleteDataSource('non-existent')).rejects.toThrow('数据源不存在');
    });
  });

  describe('testConnection', () => {
    it('should test PostgreSQL connection successfully', async () => {
      const connectionConfig = {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      // Mock the private method
      const testPostgreSQLConnectionSpy = jest.spyOn(dataSourceService as any, 'testPostgreSQLConnection');
      testPostgreSQLConnectionSpy.mockResolvedValue(true);

      const result = await dataSourceService.testConnection('postgresql', connectionConfig);

      expect(result).toBe(true);
      expect(testPostgreSQLConnectionSpy).toHaveBeenCalledWith(connectionConfig);
    });

    it('should handle connection failure', async () => {
      const connectionConfig = {
        host: 'invalid-host',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password',
      };

      const testPostgreSQLConnectionSpy = jest.spyOn(dataSourceService as any, 'testPostgreSQLConnection');
      testPostgreSQLConnectionSpy.mockRejectedValue(new Error('Connection timeout'));

      await expect(
        dataSourceService.testConnection('postgresql', connectionConfig)
      ).rejects.toThrow('连接测试失败');
    });
  });
});
