import { Request, Response, NextFunction } from 'express';
import { ChatService } from '../services/chatService';
import { prisma } from '../index';
import { validate, chatRequestSchema } from '../utils/validation';
import { ApiResponse, AppError } from '../types';

const chatService = new ChatService(prisma);

/**
 * 处理聊天查询
 */
export async function processChat(req: Request, res: Response, next: NextFunction) {
  try {
    const validatedData = validate(chatRequestSchema, req.body);
    const { sessionId, prompt } = validatedData;
    
    const response = await chatService.processQuery({ sessionId, prompt });
    
    const apiResponse: ApiResponse = {
      success: true,
      data: response
    };
    
    res.json(apiResponse);
  } catch (error) {
    next(error);
  }
}

/**
 * 获取聊天历史
 */
export async function getChatHistory(req: Request, res: Response, next: NextFunction) {
  try {
    const { sessionId } = req.params;
    const { limit } = req.query;
    
    if (!sessionId) {
      throw new AppError('会话ID不能为空', 400);
    }
    
    const limitNumber = limit ? parseInt(limit as string) : 50;
    const history = await chatService.getChatHistory(sessionId, limitNumber);
    
    const response: ApiResponse = {
      success: true,
      data: history
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}
