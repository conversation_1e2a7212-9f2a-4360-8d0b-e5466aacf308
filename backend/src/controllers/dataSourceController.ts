import { Request, Response, NextFunction } from 'express';
import { DataSourceService } from '../services/dataSourceService';
import { prisma } from '../index';
import { validate, dataSourceSchema } from '../utils/validation';
import { ApiResponse, AppError } from '../types';

const dataSourceService = new DataSourceService(prisma);

/**
 * 获取所有数据源
 */
export async function getDataSources(req: Request, res: Response, next: NextFunction) {
  try {
    const dataSources = await dataSourceService.getDataSources();
    
    const response: ApiResponse = {
      success: true,
      data: dataSources
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 根据ID获取数据源详情
 */
export async function getDataSourceById(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    const dataSource = await dataSourceService.getDataSourceById(id);
    
    const response: ApiResponse = {
      success: true,
      data: dataSource
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 获取数据源的Schema信息
 */
export async function getDataSourceSchema(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    const schema = await dataSourceService.getDataSourceById(id);
    
    const response: ApiResponse = {
      success: true,
      data: schema
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 创建新数据源
 */
export async function createDataSource(req: Request, res: Response, next: NextFunction) {
  try {
    // 验证请求数据
    const validatedData = validate(dataSourceSchema, req.body);
    const { name, type, connectionConfig } = validatedData;
    
    const dataSourceId = await dataSourceService.createDataSource(name, type, connectionConfig);
    
    const response: ApiResponse = {
      success: true,
      data: { id: dataSourceId },
      message: '数据源创建成功'
    };
    
    res.status(201).json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 更新数据源
 */
export async function updateDataSource(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    const { name, connectionConfig } = req.body;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    await dataSourceService.updateDataSource(id, name, connectionConfig);
    
    const response: ApiResponse = {
      success: true,
      message: '数据源更新成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 删除数据源
 */
export async function deleteDataSource(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    await dataSourceService.deleteDataSource(id);
    
    const response: ApiResponse = {
      success: true,
      message: '数据源删除成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 测试数据源连接
 */
export async function testConnection(req: Request, res: Response, next: NextFunction) {
  try {
    const { type, connectionConfig } = req.body;
    
    if (!type || !connectionConfig) {
      throw new AppError('数据源类型和连接配置不能为空', 400);
    }
    
    const isConnected = await dataSourceService.testConnection(type, connectionConfig);
    
    const response: ApiResponse = {
      success: true,
      data: { connected: isConnected },
      message: isConnected ? '连接测试成功' : '连接测试失败'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 同步数据源元数据
 */
export async function syncMetadata(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    await dataSourceService.syncMetadata(id);
    
    const response: ApiResponse = {
      success: true,
      message: '元数据同步成功'
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}

/**
 * 检查数据源连接状态
 */
export async function checkConnectionStatus(req: Request, res: Response, next: NextFunction) {
  try {
    const { id } = req.params;
    
    if (!id) {
      throw new AppError('数据源ID不能为空', 400);
    }
    
    const status = await dataSourceService.checkConnectionStatus(id);
    
    const response: ApiResponse = {
      success: true,
      data: { status }
    };
    
    res.json(response);
  } catch (error) {
    next(error);
  }
}
