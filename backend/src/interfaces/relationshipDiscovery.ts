import { 
  DataSourceType, 
  ConnectionConfig, 
  RelationshipDiscoveryResult, 
  RelationshipDiscoveryConfig 
} from '../types';

/**
 * 关联关系发现器抽象接口
 * 为不同数据源类型提供统一的关联关系发现能力
 */
export interface IRelationshipDiscovery {
  /**
   * 数据源类型
   */
  readonly dataSourceType: DataSourceType;

  /**
   * 发现数据源内部的关联关系
   * @param dataSourceId 数据源ID
   * @param config 连接配置
   * @param discoveryConfig 发现配置
   * @returns 发现的关联关系列表
   */
  discoverInternalRelationships(
    dataSourceId: string,
    config: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]>;

  /**
   * 发现与其他数据源的跨源关联关系
   * @param sourceDataSourceId 源数据源ID
   * @param targetDataSourceId 目标数据源ID
   * @param sourceConfig 源数据源配置
   * @param targetConfig 目标数据源配置
   * @param discoveryConfig 发现配置
   * @returns 发现的跨源关联关系列表
   */
  discoverCrossSourceRelationships(
    sourceDataSourceId: string,
    targetDataSourceId: string,
    sourceConfig: ConnectionConfig,
    targetConfig: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]>;

  /**
   * 验证关联关系的有效性
   * @param fromColumnId 起始列ID
   * @param toColumnId 目标列ID
   * @param config 连接配置
   * @returns 验证结果和置信度
   */
  validateRelationship(
    fromColumnId: string,
    toColumnId: string,
    config: ConnectionConfig
  ): Promise<{ isValid: boolean; confidence: number; evidence: string[] }>;
}

/**
 * 关联关系发现器工厂接口
 */
export interface IRelationshipDiscoveryFactory {
  /**
   * 创建指定类型的关联关系发现器
   * @param dataSourceType 数据源类型
   * @returns 关联关系发现器实例
   */
  createDiscovery(dataSourceType: DataSourceType): IRelationshipDiscovery;

  /**
   * 获取支持的数据源类型列表
   * @returns 支持的数据源类型
   */
  getSupportedTypes(): DataSourceType[];
}

/**
 * 关联关系发现服务接口
 */
export interface IRelationshipDiscoveryService {
  /**
   * 为指定数据源发现关联关系
   * @param dataSourceId 数据源ID
   * @param config 发现配置
   * @returns 发现的关联关系列表
   */
  discoverRelationships(
    dataSourceId: string,
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]>;

  /**
   * 发现跨数据源关联关系
   * @param sourceDataSourceId 源数据源ID
   * @param targetDataSourceId 目标数据源ID（可选，如果不指定则与所有其他数据源比较）
   * @param config 发现配置
   * @returns 发现的跨源关联关系列表
   */
  discoverCrossSourceRelationships(
    sourceDataSourceId: string,
    targetDataSourceId?: string,
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]>;

  /**
   * 批量发现所有数据源的关联关系
   * @param config 发现配置
   * @returns 发现的所有关联关系列表
   */
  discoverAllRelationships(
    config?: Partial<RelationshipDiscoveryConfig>
  ): Promise<RelationshipDiscoveryResult[]>;

  /**
   * 应用发现的关联关系到数据库
   * @param relationships 要应用的关联关系列表
   * @param autoApplyThreshold 自动应用的置信度阈值
   * @returns 应用结果统计
   */
  applyDiscoveredRelationships(
    relationships: RelationshipDiscoveryResult[],
    autoApplyThreshold?: number
  ): Promise<{
    applied: number;
    skipped: number;
    failed: number;
    details: Array<{
      relationship: RelationshipDiscoveryResult;
      status: 'applied' | 'skipped' | 'failed';
      reason?: string;
    }>;
  }>;
}
