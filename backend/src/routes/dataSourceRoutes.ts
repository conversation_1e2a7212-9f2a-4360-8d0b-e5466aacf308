import { Router } from 'express';
import {
  getDataSources,
  getDataSourceById,
  getDataSourceSchema,
  createDataSource,
  updateDataSource,
  deleteDataSource,
  testConnection,
  syncMetadata,
  checkConnectionStatus
} from '../controllers/dataSourceController';

const router = Router();

// 获取所有数据源
router.get('/', getDataSources);

// 测试连接（不需要先创建数据源）
router.post('/test-connection', testConnection);

// 根据ID获取数据源详情
router.get('/:id', getDataSourceById);

// 获取数据源的Schema信息
router.get('/:id/schema', getDataSourceSchema);

// 检查数据源连接状态
router.get('/:id/status', checkConnectionStatus);

// 同步数据源元数据
router.post('/:id/sync', syncMetadata);

// 创建新数据源
router.post('/', createDataSource);

// 更新数据源
router.put('/:id', updateDataSource);

// 删除数据源
router.delete('/:id', deleteDataSource);

export default router;
