import { Router } from 'express';
import {
  updateTableAlias,
  updateColumnAlias,
  createRelationship,
  updateRelationship,
  deleteRelationship,
  getSemanticContext,
  discoverRelationships,
  discoverCrossSourceRelationships,
  discoverAllRelationships,
  applyDiscoveredRelationships
} from '../controllers/semanticController';

const router = Router();

// 获取语义层上下文
router.get('/context', getSemanticContext);

// 更新表别名
router.put('/tables/:tableId/alias', updateTableAlias);

// 更新列别名
router.put('/columns/:columnId/alias', updateColumnAlias);

// 关联关系管理
router.post('/relationships', createRelationship);
router.put('/relationships/:relationshipId', updateRelationship);
router.delete('/relationships/:relationshipId', deleteRelationship);

// 自动发现关联关系
router.post('/data-sources/:dataSourceId/discover-relationships', discoverRelationships);

// 发现跨数据源关联关系
router.post('/data-sources/:sourceDataSourceId/discover-cross-relationships', discoverCrossSourceRelationships);

// 批量发现所有关联关系
router.post('/discover-all-relationships', discoverAllRelationships);

// 应用发现的关联关系
router.post('/apply-relationships', applyDiscoveredRelationships);

export default router;
