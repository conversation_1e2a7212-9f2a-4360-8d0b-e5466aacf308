import OpenAI from 'openai';
import { PrismaClient } from '@prisma/client';
import { ExternalServiceError } from '../types';

export class AIService {
  private openai: OpenAI;

  constructor(private prisma: PrismaClient) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  /**
   * 生成SQL查询
   */
  async generateSQL(userQuery: string, semanticContext: string): Promise<{ sql: string; explanation: string }> {
    try {
      const prompt = this.buildSQLPrompt(userQuery, semanticContext);
      
      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "你是一个专业的SQL查询生成助手。你需要根据用户的自然语言问题和提供的数据模型信息，生成准确的SQL查询语句。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 1000
      });

      const response = completion.choices[0]?.message?.content || '';
      return this.parseSQLResponse(response);
    } catch (error) {
      throw new ExternalServiceError(`SQL生成失败: ${error.message}`);
    }
  }

  /**
   * 生成自然语言回答
   */
  async generateNaturalLanguageResponse(
    userQuery: string, 
    queryResult: any, 
    explanation: string
  ): Promise<string> {
    try {
      const prompt = this.buildResponsePrompt(userQuery, queryResult, explanation);

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "你是一个友好的数据分析助手，能够用自然、易懂的中文解释查询结果。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      return completion.choices[0]?.message?.content || '查询完成，请查看结果数据。';
    } catch (error) {
      console.error('生成自然语言回答失败:', error);
      return '查询完成，请查看结果数据。';
    }
  }

  /**
   * 推荐可视化类型
   */
  async recommendVisualization(userQuery: string, queryResult: any): Promise<string> {
    try {
      const prompt = this.buildVisualizationPrompt(userQuery, queryResult);

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "你是一个数据可视化专家，能够根据用户问题和查询结果推荐最合适的图表类型。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 200
      });

      const response = completion.choices[0]?.message?.content || '';
      return this.parseVisualizationResponse(response);
    } catch (error) {
      console.error('推荐可视化类型失败:', error);
      return this.getDefaultVisualizationType(userQuery, queryResult);
    }
  }

  /**
   * 优化查询性能
   */
  async optimizeQuery(sql: string, semanticContext: string): Promise<string> {
    try {
      const prompt = `
请优化以下SQL查询的性能：

原始SQL:
${sql}

数据模型信息:
${semanticContext}

请提供优化后的SQL查询，考虑以下方面：
1. 索引使用
2. JOIN优化
3. WHERE条件优化
4. 子查询优化

只返回优化后的SQL语句，不需要解释。
`;

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: "你是一个SQL性能优化专家。"
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 800
      });

      const optimizedSQL = completion.choices[0]?.message?.content?.trim() || sql;
      
      // 简单验证优化后的SQL
      if (optimizedSQL.toLowerCase().includes('select')) {
        return optimizedSQL;
      }
      
      return sql; // 如果优化失败，返回原始SQL
    } catch (error) {
      console.error('SQL优化失败:', error);
      return sql;
    }
  }

  // 私有方法
  private buildSQLPrompt(userQuery: string, semanticContext: string): string {
    return `
你是一个专业的数据分析师，需要根据用户的自然语言问题生成相应的SQL查询。

## 数据模型信息：
${semanticContext}

## 用户问题：
${userQuery}

## 要求：
1. 根据上述数据模型信息，生成准确的SQL查询语句
2. 使用表和列的别名（如果有的话）来理解用户意图，但在SQL中使用原始名称
3. 确保SQL语法正确且能够执行
4. 如果问题涉及多个表，请正确使用JOIN语句
5. 对于聚合查询，请使用适当的GROUP BY和聚合函数
6. 对于时间相关查询，请使用合适的日期函数
7. 返回格式必须是JSON，包含以下字段：
   - sql: 生成的SQL查询语句
   - explanation: 对查询逻辑的简短解释

## 示例响应格式：
{
  "sql": "SELECT customer_name, SUM(amount) as total_amount FROM customers c JOIN orders o ON c.id = o.customer_id WHERE o.created_at >= CURRENT_DATE - INTERVAL '30 days' GROUP BY c.id, customer_name ORDER BY total_amount DESC LIMIT 10",
  "explanation": "查询最近30天内每个客户的总订单金额，按金额降序排列，取前10名"
}

## 注意事项：
- 如果用户问题模糊，请根据常见的业务场景进行合理推测
- 对于"最近"、"本月"等时间表述，请转换为具体的日期条件
- 确保生成的SQL能够在PostgreSQL或MySQL中正常执行
`;
  }

  private buildResponsePrompt(userQuery: string, queryResult: any, explanation: string): string {
    const resultSummary = this.summarizeQueryResult(queryResult);
    
    return `
根据用户问题和查询结果，生成一个自然、友好的回答。

用户问题：${userQuery}
查询说明：${explanation}
查询结果摘要：${resultSummary}

要求：
1. 用中文生成一个简洁、准确的回答
2. 直接说明查询结果的关键信息
3. 如果有数值，请用易读的格式表示（如：1,000,000 而不是 1000000）
4. 语气要友好、专业
5. 不要重复用户的问题，直接给出答案

示例：
- "根据查询结果，最近一个月的总销售额为 1,234,567 元。"
- "销售额最高的客户是张三，总消费金额为 58,000 元。"
- "各产品类别中，电子产品占比最高，达到 45.2%。"
`;
  }

  private buildVisualizationPrompt(userQuery: string, queryResult: any): string {
    const dataInfo = this.analyzeQueryResult(queryResult);
    
    return `
根据用户问题和查询结果，推荐最合适的图表类型。

用户问题：${userQuery}
数据信息：${dataInfo}

可选的图表类型：
- bar_chart: 柱状图（适合比较不同类别的数值）
- line_chart: 折线图（适合显示趋势变化）
- pie_chart: 饼图（适合显示占比分布）
- table: 表格（适合显示详细数据）
- scatter_plot: 散点图（适合显示两个变量的关系）

请只返回最合适的图表类型名称，不需要解释。
`;
  }

  private parseSQLResponse(response: string): { sql: string; explanation: string } {
    try {
      // 尝试解析JSON响应
      const parsed = JSON.parse(response);
      return {
        sql: parsed.sql || '',
        explanation: parsed.explanation || ''
      };
    } catch (error) {
      // 如果不是JSON格式，尝试提取SQL
      const sqlMatch = response.match(/```sql\n([\s\S]*?)\n```/);
      if (sqlMatch) {
        return {
          sql: sqlMatch[1].trim(),
          explanation: '从响应中提取的SQL查询'
        };
      }
      
      // 尝试提取JSON中的SQL
      const jsonMatch = response.match(/"sql":\s*"([^"]+)"/);
      if (jsonMatch) {
        const explanationMatch = response.match(/"explanation":\s*"([^"]+)"/);
        return {
          sql: jsonMatch[1],
          explanation: explanationMatch ? explanationMatch[1] : '生成的SQL查询'
        };
      }
      
      throw new Error('无法解析LLM响应');
    }
  }

  private parseVisualizationResponse(response: string): string {
    const validTypes = ['bar_chart', 'line_chart', 'pie_chart', 'table', 'scatter_plot'];
    const lowerResponse = response.toLowerCase().trim();
    
    for (const type of validTypes) {
      if (lowerResponse.includes(type)) {
        return type;
      }
    }
    
    return 'bar_chart'; // 默认返回柱状图
  }

  private getDefaultVisualizationType(userQuery: string, queryResult: any): string {
    const query = userQuery.toLowerCase();
    
    if (query.includes('趋势') || query.includes('时间') || query.includes('月') || query.includes('年')) {
      return 'line_chart';
    } else if (query.includes('占比') || query.includes('比例') || query.includes('分布')) {
      return 'pie_chart';
    } else if (queryResult?.rows?.length > 20) {
      return 'table';
    } else {
      return 'bar_chart';
    }
  }

  private summarizeQueryResult(queryResult: any): string {
    if (!queryResult || !queryResult.rows) {
      return '无查询结果';
    }
    
    const rowCount = queryResult.rows.length;
    const columnCount = queryResult.columns?.length || 0;
    
    return `查询返回 ${rowCount} 行数据，包含 ${columnCount} 个字段`;
  }

  private analyzeQueryResult(queryResult: any): string {
    if (!queryResult || !queryResult.rows || !queryResult.columns) {
      return '无有效数据';
    }
    
    const rowCount = queryResult.rows.length;
    const columnCount = queryResult.columns.length;
    const hasNumericData = queryResult.rows.some((row: any[]) => 
      row.some(cell => typeof cell === 'number')
    );
    
    return `${rowCount} 行 ${columnCount} 列数据，${hasNumericData ? '包含' : '不包含'}数值型数据`;
  }
}
