import { PrismaClient } from '@prisma/client';
import { ChatRequest, ChatResponse, VisualizationType, AppError, ExternalServiceError } from '../types';
import { SemanticService } from './semanticService';
import { AIService } from './aiService';

export class ChatService {
  private semanticService: SemanticService;
  private aiService: AIService;

  constructor(private prisma: PrismaClient) {
    this.semanticService = new SemanticService(prisma);
    this.aiService = new AIService(prisma);
  }

  /**
   * 处理自然语言查询
   */
  async processQuery(request: ChatRequest): Promise<ChatResponse> {
    try {
      // 1. 获取语义层上下文
      const semanticContext = await this.semanticService.getSemanticContext();

      // 2. 调用AI服务生成SQL
      const { sql, explanation } = await this.aiService.generateSQL(request.prompt, semanticContext);

      // 3. 执行SQL查询
      const queryResult = await this.executeQuery(sql);

      // 4. 生成自然语言回答
      const naturalLanguageResponse = await this.aiService.generateNaturalLanguageResponse(
        request.prompt,
        queryResult,
        explanation
      );

      // 5. 生成可视化配置
      const visualization = await this.generateVisualization(request.prompt, queryResult);

      // 6. 保存聊天记录
      const chatHistory = await this.saveChatHistory({
        sessionId: request.sessionId,
        userQuery: request.prompt,
        generatedSql: sql,
        queryResultData: queryResult,
        naturalLanguageResponse,
        visualizationSpec: visualization
      });

      return {
        id: chatHistory.id,
        sessionId: request.sessionId,
        userPrompt: request.prompt,
        responseText: naturalLanguageResponse,
        data: this.formatQueryResult(queryResult),
        visualization,
        generatedSql: sql
      };
    } catch (error) {
      throw new ExternalServiceError(`查询处理失败: ${error.message}`);
    }
  }

  /**
   * 获取聊天历史
   */
  async getChatHistory(sessionId: string, limit: number = 50): Promise<ChatResponse[]> {
    try {
      const chatHistory = await this.prisma.chatHistory.findMany({
        where: { sessionId },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return chatHistory.map(chat => ({
        id: chat.id,
        sessionId: chat.sessionId,
        userPrompt: chat.userQuery,
        responseText: chat.naturalLanguageResponse || '',
        data: this.formatQueryResult(chat.queryResultData),
        visualization: chat.visualizationSpec as any,
        generatedSql: chat.generatedSql || undefined
      }));
    } catch (error) {
      throw new AppError('获取聊天历史失败', 500);
    }
  }

  // 私有方法

  private async executeQuery(sql: string): Promise<any> {
    try {
      // 这里应该根据实际的数据源执行查询
      // 暂时返回模拟数据
      return {
        columns: ['客户姓名', '总销售额'],
        rows: [
          ['张三', 5800],
          ['李四', 4200],
          ['王五', 3600]
        ]
      };
    } catch (error) {
      throw new Error(`SQL执行失败: ${error.message}`);
    }
  }



  private async generateVisualization(userQuery: string, queryResult: any): Promise<any> {
    try {
      // 使用AI服务推荐可视化类型
      const visualizationType = await this.aiService.recommendVisualization(userQuery, queryResult);

      return this.createVisualizationSpec(visualizationType, queryResult);
    } catch (error) {
      // 如果AI推荐失败，使用简单的规则
      return this.createVisualizationSpec(this.getDefaultVisualizationType(userQuery), queryResult);
    }
  }

  private createVisualizationSpec(type: string, queryResult: any): any {
    const data = this.convertToVegaData(queryResult);
    const columns = queryResult.columns || [];

    switch (type) {
      case 'line_chart':
        return {
          type: 'line_chart',
          title: '趋势图',
          spec: {
            data: { values: data },
            mark: 'line',
            encoding: {
              x: { field: columns[0], type: 'temporal' },
              y: { field: columns[1], type: 'quantitative' }
            }
          }
        };
      case 'pie_chart':
        return {
          type: 'pie_chart',
          title: '分布图',
          spec: {
            data: { values: data },
            mark: 'arc',
            encoding: {
              theta: { field: columns[1], type: 'quantitative' },
              color: { field: columns[0], type: 'nominal' }
            }
          }
        };
      case 'table':
        return {
          type: 'table',
          title: '数据表格',
          spec: { data: queryResult }
        };
      default:
        return {
          type: 'bar_chart',
          title: '柱状图',
          spec: {
            data: { values: data },
            mark: 'bar',
            encoding: {
              x: { field: columns[0], type: 'nominal' },
              y: { field: columns[1], type: 'quantitative' }
            }
          }
        };
    }
  }

  private getDefaultVisualizationType(userQuery: string): string {
    const query = userQuery.toLowerCase();

    if (query.includes('趋势') || query.includes('时间') || query.includes('月') || query.includes('年')) {
      return 'line_chart';
    } else if (query.includes('占比') || query.includes('比例') || query.includes('分布')) {
      return 'pie_chart';
    } else {
      return 'bar_chart';
    }
  }

  private convertToVegaData(queryResult: any): any[] {
    if (!queryResult.columns || !queryResult.rows) {
      return [];
    }

    return queryResult.rows.map((row: any[]) => {
      const obj: any = {};
      queryResult.columns.forEach((col: string, index: number) => {
        obj[col] = row[index];
      });
      return obj;
    });
  }

  private formatQueryResult(queryResult: any): any {
    if (!queryResult) return null;
    
    return {
      columns: queryResult.columns || [],
      rows: queryResult.rows || []
    };
  }

  private async saveChatHistory(data: any): Promise<any> {
    return await this.prisma.chatHistory.create({
      data: {
        sessionId: data.sessionId,
        userQuery: data.userQuery,
        generatedSql: data.generatedSql,
        queryResultData: data.queryResultData,
        naturalLanguageResponse: data.naturalLanguageResponse,
        visualizationSpec: data.visualizationSpec
      }
    });
  }
}
