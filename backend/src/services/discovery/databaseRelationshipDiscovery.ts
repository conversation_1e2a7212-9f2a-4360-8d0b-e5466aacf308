import { Client as PgClient } from 'pg';
import mysql from 'mysql2/promise';
import { PrismaClient } from '@prisma/client';

import {
  DataSourceType,
  ConnectionConfig,
  DatabaseConnectionConfig,
  RelationshipDiscoveryResult,
  RelationshipDiscoveryConfig,
  RelationshipType,
  ConfidenceLevel,
  RelationshipDiscoveryMethod
} from '../../types';
import { IRelationshipDiscovery } from '../../interfaces/relationshipDiscovery';
import { AppError } from '../../types';

/**
 * 数据库关联关系发现器
 * 支持PostgreSQL和MySQL的外键约束检测和命名约定匹配
 */
export class DatabaseRelationshipDiscovery implements IRelationshipDiscovery {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient, public readonly dataSourceType: DataSourceType) {
    this.prisma = prisma;
    if (dataSourceType !== 'postgresql' && dataSourceType !== 'mysql') {
      throw new Error(`不支持的数据库类型: ${dataSourceType}`);
    }
  }

  /**
   * 发现数据源内部的关联关系
   */
  async discoverInternalRelationships(
    dataSourceId: string,
    config: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    const results: RelationshipDiscoveryResult[] = [];
    const dbConfig = config as DatabaseConnectionConfig;

    // 1. 外键约束检测
    if (discoveryConfig.enableForeignKeyDetection) {
      const foreignKeyResults = await this.discoverForeignKeyRelationships(
        dataSourceId,
        dbConfig
      );
      results.push(...foreignKeyResults);
    }

    // 2. 命名约定匹配
    if (discoveryConfig.enableNamingConvention) {
      const namingResults = await this.discoverNamingConventionRelationships(
        dataSourceId,
        discoveryConfig.namingPatterns
      );
      results.push(...namingResults);
    }

    // 3. 数据分析（如果启用）
    if (discoveryConfig.enableDataAnalysis) {
      const dataAnalysisResults = await this.discoverDataAnalysisRelationships(
        dataSourceId,
        dbConfig
      );
      results.push(...dataAnalysisResults);
    }

    // 过滤重复和低置信度的结果
    return this.filterAndRankResults(results, discoveryConfig);
  }

  /**
   * 发现跨数据源关联关系
   */
  async discoverCrossSourceRelationships(
    sourceDataSourceId: string,
    targetDataSourceId: string,
    sourceConfig: ConnectionConfig,
    targetConfig: ConnectionConfig,
    discoveryConfig: RelationshipDiscoveryConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    if (!discoveryConfig.enableCrossDataSource) {
      return [];
    }

    // 获取两个数据源的表和列信息
    const [sourceTables, targetTables] = await Promise.all([
      this.getDataSourceTables(sourceDataSourceId),
      this.getDataSourceTables(targetDataSourceId)
    ]);

    const results: RelationshipDiscoveryResult[] = [];

    // 基于命名约定进行跨源匹配
    for (const sourceTable of sourceTables) {
      for (const sourceColumn of sourceTable.dataColumns) {
        for (const targetTable of targetTables) {
          for (const targetColumn of targetTable.dataColumns) {
            const relationship = await this.analyzeColumnRelationship(
              sourceColumn,
              targetColumn,
              'naming_convention'
            );
            if (relationship) {
              results.push(relationship);
            }
          }
        }
      }
    }

    return this.filterAndRankResults(results, discoveryConfig);
  }

  /**
   * 验证关联关系的有效性
   */
  async validateRelationship(
    fromColumnId: string,
    toColumnId: string,
    config: ConnectionConfig
  ): Promise<{ isValid: boolean; confidence: number; evidence: string[] }> {
    const [fromColumn, toColumn] = await Promise.all([
      this.prisma.dataColumn.findUnique({
        where: { id: fromColumnId },
        include: { dataTable: true }
      }),
      this.prisma.dataColumn.findUnique({
        where: { id: toColumnId },
        include: { dataTable: true }
      })
    ]);

    if (!fromColumn || !toColumn) {
      return { isValid: false, confidence: 0, evidence: ['列不存在'] };
    }

    const evidence: string[] = [];
    let confidence = 0;

    // 检查数据类型兼容性
    if (this.areDataTypesCompatible(fromColumn.originalDataType, toColumn.originalDataType)) {
      evidence.push('数据类型兼容');
      confidence += 0.3;
    }

    // 检查命名约定
    if (this.matchesNamingConvention(fromColumn.originalColumnName, toColumn.originalColumnName)) {
      evidence.push('符合命名约定');
      confidence += 0.4;
    }

    // 检查是否为主键-外键关系
    if (fromColumn.isPrimaryKey || toColumn.isPrimaryKey) {
      evidence.push('涉及主键');
      confidence += 0.3;
    }

    return {
      isValid: confidence > 0.5,
      confidence,
      evidence
    };
  }

  /**
   * 通过外键约束发现关联关系
   */
  private async discoverForeignKeyRelationships(
    dataSourceId: string,
    config: DatabaseConnectionConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    if (this.dataSourceType === 'postgresql') {
      return this.discoverPostgreSQLForeignKeys(dataSourceId, config);
    } else if (this.dataSourceType === 'mysql') {
      return this.discoverMySQLForeignKeys(dataSourceId, config);
    }
    return [];
  }

  /**
   * 发现PostgreSQL外键关系
   */
  private async discoverPostgreSQLForeignKeys(
    dataSourceId: string,
    config: DatabaseConnectionConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    const client = new PgClient({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl
    });

    try {
      await client.connect();

      // 查询外键约束信息
      const foreignKeysResult = await client.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name as source_table,
          kcu.column_name as source_column,
          ccu.table_name as target_table,
          ccu.column_name as target_column,
          rc.match_option,
          rc.update_rule,
          rc.delete_rule
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage ccu 
          ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.referential_constraints rc 
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
        ORDER BY tc.table_name, kcu.ordinal_position
      `);

      const results: RelationshipDiscoveryResult[] = [];

      for (const fk of foreignKeysResult.rows) {
        const relationship = await this.createForeignKeyRelationship(
          dataSourceId,
          fk.source_table,
          fk.source_column,
          fk.target_table,
          fk.target_column
        );
        if (relationship) {
          results.push(relationship);
        }
      }

      return results;
    } finally {
      await client.end();
    }
  }

  /**
   * 发现MySQL外键关系
   */
  private async discoverMySQLForeignKeys(
    dataSourceId: string,
    config: DatabaseConnectionConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.username,
      password: config.password,
      ssl: config.ssl
    });

    try {
      // 查询外键约束信息
      const [foreignKeysResult] = await connection.execute(`
        SELECT 
          kcu.constraint_name,
          kcu.table_name as source_table,
          kcu.column_name as source_column,
          kcu.referenced_table_name as target_table,
          kcu.referenced_column_name as target_column,
          rc.update_rule,
          rc.delete_rule
        FROM information_schema.key_column_usage kcu
        JOIN information_schema.referential_constraints rc 
          ON kcu.constraint_name = rc.constraint_name
        WHERE kcu.table_schema = ?
        AND kcu.referenced_table_name IS NOT NULL
        ORDER BY kcu.table_name, kcu.ordinal_position
      `, [config.database]);

      const results: RelationshipDiscoveryResult[] = [];

      for (const fk of foreignKeysResult as any[]) {
        const relationship = await this.createForeignKeyRelationship(
          dataSourceId,
          fk.source_table,
          fk.source_column,
          fk.target_table,
          fk.target_column
        );
        if (relationship) {
          results.push(relationship);
        }
      }

      return results;
    } finally {
      await connection.end();
    }
  }

  /**
   * 创建外键关联关系结果
   */
  private async createForeignKeyRelationship(
    dataSourceId: string,
    sourceTableName: string,
    sourceColumnName: string,
    targetTableName: string,
    targetColumnName: string
  ): Promise<RelationshipDiscoveryResult | null> {
    // 查找对应的列ID
    const [sourceColumn, targetColumn] = await Promise.all([
      this.prisma.dataColumn.findFirst({
        where: {
          originalColumnName: sourceColumnName,
          dataTable: {
            originalTableName: sourceTableName,
            dataSourceId
          }
        }
      }),
      this.prisma.dataColumn.findFirst({
        where: {
          originalColumnName: targetColumnName,
          dataTable: {
            originalTableName: targetTableName,
            dataSourceId
          }
        }
      })
    ]);

    if (!sourceColumn || !targetColumn) {
      return null;
    }

    return {
      fromColumnId: targetColumn.id, // 主键端
      toColumnId: sourceColumn.id,   // 外键端
      relationshipType: 'one_to_many',
      discoveryMethod: 'foreign_key',
      confidence: 'high',
      confidenceScore: 0.95,
      evidence: [
        '数据库外键约束',
        `${sourceTableName}.${sourceColumnName} -> ${targetTableName}.${targetColumnName}`
      ],
      metadata: {
        constraintName: `${sourceTableName}_${sourceColumnName}_fkey`,
        sourceTable: sourceTableName,
        targetTable: targetTableName
      }
    };
  }

  /**
   * 通过命名约定发现关联关系
   */
  private async discoverNamingConventionRelationships(
    dataSourceId: string,
    namingPatterns: string[]
  ): Promise<RelationshipDiscoveryResult[]> {
    const tables = await this.getDataSourceTables(dataSourceId);
    const results: RelationshipDiscoveryResult[] = [];

    for (const table of tables) {
      for (const column of table.dataColumns) {
        if (column.isPrimaryKey) {
          // 查找可能的外键
          const potentialForeignKeys = this.findPotentialForeignKeys(
            column.originalColumnName,
            table.id,
            tables,
            namingPatterns
          );

          for (const fk of potentialForeignKeys) {
            const relationship: RelationshipDiscoveryResult = {
              fromColumnId: column.id,
              toColumnId: fk.id,
              relationshipType: 'one_to_many',
              discoveryMethod: 'naming_convention',
              confidence: this.calculateNamingConfidence(column.originalColumnName, fk.originalColumnName),
              confidenceScore: this.calculateNamingConfidenceScore(column.originalColumnName, fk.originalColumnName),
              evidence: [
                '命名约定匹配',
                `${column.originalColumnName} -> ${fk.originalColumnName}`
              ]
            };
            results.push(relationship);
          }
        }
      }
    }

    return results;
  }

  /**
   * 通过数据分析发现关联关系
   */
  private async discoverDataAnalysisRelationships(
    dataSourceId: string,
    config: DatabaseConnectionConfig
  ): Promise<RelationshipDiscoveryResult[]> {
    // 这里可以实现基于数据分布、唯一性等的分析
    // 暂时返回空数组，后续可以扩展
    return [];
  }

  /**
   * 过滤和排序结果
   */
  private filterAndRankResults(
    results: RelationshipDiscoveryResult[],
    config: RelationshipDiscoveryConfig
  ): RelationshipDiscoveryResult[] {
    // 去重
    const uniqueResults = this.removeDuplicateRelationships(results);

    // 按置信度过滤
    const filteredResults = uniqueResults.filter(
      r => r.confidenceScore >= config.confidenceThreshold
    );

    // 按置信度排序
    filteredResults.sort((a, b) => b.confidenceScore - a.confidenceScore);

    // 限制数量
    return filteredResults.slice(0, config.maxSuggestions);
  }

  /**
   * 移除重复的关联关系
   */
  private removeDuplicateRelationships(
    results: RelationshipDiscoveryResult[]
  ): RelationshipDiscoveryResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      const key = `${result.fromColumnId}-${result.toColumnId}`;
      const reverseKey = `${result.toColumnId}-${result.fromColumnId}`;

      if (seen.has(key) || seen.has(reverseKey)) {
        return false;
      }

      seen.add(key);
      return true;
    });
  }

  /**
   * 获取数据源的表信息
   */
  private async getDataSourceTables(dataSourceId: string) {
    return this.prisma.dataTable.findMany({
      where: { dataSourceId },
      include: { dataColumns: true }
    });
  }

  /**
   * 查找潜在的外键
   */
  private findPotentialForeignKeys(
    primaryKeyName: string,
    excludeTableId: string,
    allTables: any[],
    namingPatterns: string[]
  ): any[] {
    const potentialForeignKeys = [];

    // 使用配置的命名模式
    const patterns = namingPatterns.length > 0 ? namingPatterns : [
      primaryKeyName, // 完全匹配
      `${primaryKeyName.replace('_id', '')}_id`, // 如果主键是 id，查找 table_id
      `${primaryKeyName.replace('id', '')}_id` // 其他变体
    ];

    for (const table of allTables) {
      if (table.id === excludeTableId) continue;

      for (const column of table.dataColumns) {
        if (this.matchesAnyPattern(column.originalColumnName, patterns)) {
          potentialForeignKeys.push(column);
        }
      }
    }

    return potentialForeignKeys;
  }

  /**
   * 检查列名是否匹配任何模式
   */
  private matchesAnyPattern(columnName: string, patterns: string[]): boolean {
    const lowerColumnName = columnName.toLowerCase();
    return patterns.some(pattern => {
      const lowerPattern = pattern.toLowerCase();
      return lowerColumnName === lowerPattern ||
             lowerColumnName.includes(lowerPattern) ||
             this.fuzzyMatch(lowerColumnName, lowerPattern);
    });
  }

  /**
   * 模糊匹配
   */
  private fuzzyMatch(str1: string, str2: string): boolean {
    // 简单的编辑距离匹配
    const maxLength = Math.max(str1.length, str2.length);
    const distance = this.levenshteinDistance(str1, str2);
    return distance / maxLength < 0.3; // 相似度阈值
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 分析两列之间的关联关系
   */
  private async analyzeColumnRelationship(
    sourceColumn: any,
    targetColumn: any,
    method: RelationshipDiscoveryMethod
  ): Promise<RelationshipDiscoveryResult | null> {
    // 检查数据类型兼容性
    if (!this.areDataTypesCompatible(sourceColumn.originalDataType, targetColumn.originalDataType)) {
      return null;
    }

    let confidence = 0;
    const evidence: string[] = [];

    // 基于方法计算置信度
    switch (method) {
      case 'naming_convention':
        if (this.matchesNamingConvention(sourceColumn.originalColumnName, targetColumn.originalColumnName)) {
          confidence = this.calculateNamingConfidenceScore(sourceColumn.originalColumnName, targetColumn.originalColumnName);
          evidence.push('命名约定匹配');
        }
        break;
      // 其他方法可以在这里添加
    }

    if (confidence < 0.3) {
      return null;
    }

    return {
      fromColumnId: sourceColumn.id,
      toColumnId: targetColumn.id,
      relationshipType: this.determineRelationshipType(sourceColumn, targetColumn),
      discoveryMethod: method,
      confidence: this.scoreToConfidenceLevel(confidence),
      confidenceScore: confidence,
      evidence,
      metadata: {
        sourceTable: sourceColumn.dataTable.originalTableName,
        targetTable: targetColumn.dataTable.originalTableName
      }
    };
  }

  /**
   * 检查数据类型是否兼容
   */
  private areDataTypesCompatible(type1: string, type2: string): boolean {
    // 标准化数据类型
    const normalizedType1 = this.normalizeDataType(type1);
    const normalizedType2 = this.normalizeDataType(type2);

    // 完全匹配
    if (normalizedType1 === normalizedType2) {
      return true;
    }

    // 兼容的数据类型组合
    const compatibleTypes = [
      ['integer', 'bigint', 'smallint'],
      ['varchar', 'text', 'char'],
      ['decimal', 'numeric', 'float', 'double'],
      ['timestamp', 'datetime', 'date']
    ];

    return compatibleTypes.some(group =>
      group.includes(normalizedType1) && group.includes(normalizedType2)
    );
  }

  /**
   * 标准化数据类型
   */
  private normalizeDataType(dataType: string): string {
    const type = dataType.toLowerCase().replace(/\([^)]*\)/g, ''); // 移除括号内容

    // 映射常见的数据类型别名
    const typeMapping: Record<string, string> = {
      'int': 'integer',
      'int4': 'integer',
      'int8': 'bigint',
      'int2': 'smallint',
      'varchar': 'varchar',
      'character varying': 'varchar',
      'char': 'char',
      'character': 'char',
      'text': 'text',
      'decimal': 'decimal',
      'numeric': 'numeric',
      'float': 'float',
      'float4': 'float',
      'float8': 'double',
      'double precision': 'double',
      'timestamp': 'timestamp',
      'timestamptz': 'timestamp',
      'datetime': 'datetime',
      'date': 'date'
    };

    return typeMapping[type] || type;
  }

  /**
   * 检查是否符合命名约定
   */
  private matchesNamingConvention(name1: string, name2: string): boolean {
    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    // 完全匹配
    if (lower1 === lower2) return true;

    // 主键-外键模式匹配
    const patterns = [
      // id -> table_id
      () => lower1 === 'id' && lower2.endsWith('_id'),
      // table_id -> id
      () => lower1.endsWith('_id') && lower2 === 'id',
      // customer_id -> customer_id
      () => lower1 === lower2,
      // 去掉前缀后匹配
      () => {
        const base1 = lower1.replace(/^(.*_)?/, '');
        const base2 = lower2.replace(/^(.*_)?/, '');
        return base1 === base2 && base1.length > 2;
      }
    ];

    return patterns.some(pattern => pattern());
  }

  /**
   * 计算命名约定置信度等级
   */
  private calculateNamingConfidence(name1: string, name2: string): ConfidenceLevel {
    const score = this.calculateNamingConfidenceScore(name1, name2);
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * 计算命名约定置信度分数
   */
  private calculateNamingConfidenceScore(name1: string, name2: string): number {
    const lower1 = name1.toLowerCase();
    const lower2 = name2.toLowerCase();

    // 完全匹配
    if (lower1 === lower2) return 0.9;

    // 标准主键-外键模式
    if ((lower1 === 'id' && lower2.endsWith('_id')) ||
        (lower1.endsWith('_id') && lower2 === 'id')) {
      return 0.8;
    }

    // 相似度匹配
    const similarity = 1 - (this.levenshteinDistance(lower1, lower2) / Math.max(lower1.length, lower2.length));
    return Math.max(0, similarity - 0.2); // 减去基础阈值
  }

  /**
   * 确定关联关系类型
   */
  private determineRelationshipType(sourceColumn: any, targetColumn: any): RelationshipType {
    // 如果源列是主键，通常是一对多关系
    if (sourceColumn.isPrimaryKey) {
      return 'one_to_many';
    }

    // 如果目标列是主键，通常是多对一关系
    if (targetColumn.isPrimaryKey) {
      return 'many_to_one';
    }

    // 默认为多对一
    return 'many_to_one';
  }

  /**
   * 将分数转换为置信度等级
   */
  private scoreToConfidenceLevel(score: number): ConfidenceLevel {
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }
}
}
