import { PrismaClient } from '@prisma/client';
import { DataSourceType } from '../../types';
import { IRelationshipDiscovery, IRelationshipDiscoveryFactory } from '../../interfaces/relationshipDiscovery';
import { DatabaseRelationshipDiscovery } from './databaseRelationshipDiscovery';
import { FileRelationshipDiscovery } from './fileRelationshipDiscovery';

/**
 * 关联关系发现器工厂
 * 根据数据源类型创建相应的关联关系发现器
 */
export class RelationshipDiscoveryFactory implements IRelationshipDiscoveryFactory {
  private prisma: PrismaClient;
  private discoveryInstances: Map<DataSourceType, IRelationshipDiscovery> = new Map();

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 创建指定类型的关联关系发现器
   */
  createDiscovery(dataSourceType: DataSourceType): IRelationshipDiscovery {
    // 使用单例模式，避免重复创建实例
    if (this.discoveryInstances.has(dataSourceType)) {
      return this.discoveryInstances.get(dataSourceType)!;
    }

    let discovery: IRelationshipDiscovery;

    switch (dataSourceType) {
      case 'postgresql':
      case 'mysql':
        discovery = new DatabaseRelationshipDiscovery(this.prisma, dataSourceType);
        break;
      
      case 'csv':
      case 'excel':
        discovery = new FileRelationshipDiscovery(this.prisma, dataSourceType);
        break;
      
      default:
        throw new Error(`不支持的数据源类型: ${dataSourceType}`);
    }

    this.discoveryInstances.set(dataSourceType, discovery);
    return discovery;
  }

  /**
   * 获取支持的数据源类型列表
   */
  getSupportedTypes(): DataSourceType[] {
    return ['postgresql', 'mysql', 'csv', 'excel'];
  }

  /**
   * 清除缓存的发现器实例
   */
  clearCache(): void {
    this.discoveryInstances.clear();
  }

  /**
   * 检查是否支持指定的数据源类型
   */
  isSupported(dataSourceType: DataSourceType): boolean {
    return this.getSupportedTypes().includes(dataSourceType);
  }
}
