import {
  BusinessValidation,
  LLMEnhancementConfig,
  RelationshipDiscoveryResult,
  FieldInfo
} from '../../types';
import { LLMClient } from './llmClient';

/**
 * LLM业务逻辑验证器
 * 基于大模型的业务知识进行关联关系的逻辑推理和验证
 */
export class LLMBusinessValidator {
  private llmClient: LLMClient;
  private config: LLMEnhancementConfig;
  private businessRulesCache: Map<string, BusinessValidation> = new Map();

  // 常见业务实体关系知识库
  private readonly BUSINESS_KNOWLEDGE = {
    commonEntities: [
      'customer', 'order', 'product', 'user', 'employee', 'department',
      'invoice', 'payment', 'address', 'category', 'supplier', 'inventory'
    ],
    commonRelationships: [
      { from: 'customer', to: 'order', type: 'one_to_many', confidence: 0.9 },
      { from: 'order', to: 'order_item', type: 'one_to_many', confidence: 0.95 },
      { from: 'product', to: 'order_item', type: 'one_to_many', confidence: 0.9 },
      { from: 'user', to: 'role', type: 'many_to_many', confidence: 0.8 },
      { from: 'department', to: 'employee', type: 'one_to_many', confidence: 0.9 },
      { from: 'category', to: 'product', type: 'one_to_many', confidence: 0.85 }
    ],
    businessRules: [
      '客户可以有多个订单，但每个订单只属于一个客户',
      '订单可以包含多个商品，每个商品可以出现在多个订单中',
      '员工属于一个部门，部门可以有多个员工',
      '用户可以有多个角色，角色可以分配给多个用户',
      '产品属于一个分类，分类可以包含多个产品'
    ]
  };

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
    this.llmClient = new LLMClient(config);
  }

  /**
   * 验证关联关系的业务逻辑合理性
   */
  async validateBusinessLogic(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    businessContext?: any
  ): Promise<BusinessValidation> {
    const cacheKey = this.generateCacheKey(relationship, fromField, toField);
    
    // 检查缓存
    if (this.config.enableCache && this.businessRulesCache.has(cacheKey)) {
      return this.businessRulesCache.get(cacheKey)!;
    }

    try {
      const prompt = this.buildBusinessValidationPrompt(
        relationship, 
        fromField, 
        toField, 
        businessContext
      );

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      const validation = this.parseBusinessValidationResponse(response.content);
      
      // 缓存结果
      if (this.config.enableCache) {
        this.businessRulesCache.set(cacheKey, validation);
      }

      return validation;
    } catch (error) {
      console.error('LLM业务逻辑验证失败:', error);
      return this.getDefaultBusinessValidation(relationship, fromField, toField);
    }
  }

  /**
   * 批量验证多个关联关系的业务逻辑
   */
  async validateBatchBusinessLogic(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
    }>,
    businessContext?: any
  ): Promise<BusinessValidation[]> {
    const batchSize = this.config.batchSize;
    const results: BusinessValidation[] = [];

    for (let i = 0; i < relationships.length; i += batchSize) {
      const batch = relationships.slice(i, i + batchSize);
      const batchPrompt = this.buildBatchBusinessValidationPrompt(batch, businessContext);

      try {
        const response = await this.llmClient.chat({
          messages: [{ role: 'user', content: batchPrompt }],
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens * batch.length,
          response_format: { type: 'json_object' }
        });

        const batchResults = this.parseBatchBusinessValidationResponse(response.content, batch);
        results.push(...batchResults);
      } catch (error) {
        console.error('批量LLM业务逻辑验证失败:', error);
        const defaultResults = batch.map(item => 
          this.getDefaultBusinessValidation(item.relationship, item.fromField, item.toField)
        );
        results.push(...defaultResults);
      }
    }

    return results;
  }

  /**
   * 构建业务验证提示词
   */
  private buildBusinessValidationPrompt(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    businessContext?: any
  ): string {
    const language = this.config.language;
    const contextInfo = businessContext ? `
业务上下文：
- 行业领域: ${businessContext.industry || '通用'}
- 系统类型: ${businessContext.systemType || '未知'}
- 特殊业务规则: ${businessContext.customRules?.join(', ') || '无'}
` : '';

    if (language === 'zh') {
      return `
作为业务分析专家，请验证以下数据关联关系的业务逻辑合理性：

关联关系信息：
- 源字段: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- 目标字段: ${toField.tableName}.${toField.name} (${toField.dataType})
- 关联类型: ${relationship.relationshipType}
- 发现方法: ${relationship.discoveryMethod}
- 当前置信度: ${relationship.confidenceScore}

字段详细信息：
源字段 - ${fromField.name}:
- 描述: ${fromField.description || '无'}
- 是否主键: ${fromField.isPrimaryKey ? '是' : '否'}
- 样本数据: ${fromField.sampleData?.slice(0, 3).join(', ') || '无'}

目标字段 - ${toField.name}:
- 描述: ${toField.description || '无'}
- 是否主键: ${toField.isPrimaryKey ? '是' : '否'}
- 样本数据: ${toField.sampleData?.slice(0, 3).join(', ') || '无'}

${contextInfo}

常见业务实体关系参考：
${this.BUSINESS_KNOWLEDGE.businessRules.map(rule => `- ${rule}`).join('\n')}

请从业务逻辑角度分析并以JSON格式返回验证结果：
{
  "isBusinessLogical": true/false, // 是否符合业务逻辑
  "businessReasoningScore": 0.0-1.0, // 业务推理分数
  "potentialRisks": ["风险1", "风险2"], // 可能存在的业务风险
  "recommendations": ["建议1", "建议2"], // 改进建议
  "domainKnowledgeApplied": ["应用的领域知识1", "应用的领域知识2"] // 应用的领域知识
}

分析要点：
1. 评估关联关系是否符合常见的业务逻辑模式
2. 考虑数据完整性和一致性要求
3. 识别可能的业务风险和数据质量问题
4. 提供基于业务最佳实践的改进建议
5. 考虑特定行业或领域的业务规则
`;
    } else {
      return `
As a business analysis expert, please validate the business logic reasonableness of the following data relationship:

Relationship Information:
- Source Field: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- Target Field: ${toField.tableName}.${toField.name} (${toField.dataType})
- Relationship Type: ${relationship.relationshipType}
- Discovery Method: ${relationship.discoveryMethod}
- Current Confidence: ${relationship.confidenceScore}

Field Details:
Source Field - ${fromField.name}:
- Description: ${fromField.description || 'None'}
- Is Primary Key: ${fromField.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${fromField.sampleData?.slice(0, 3).join(', ') || 'None'}

Target Field - ${toField.name}:
- Description: ${toField.description || 'None'}
- Is Primary Key: ${toField.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${toField.sampleData?.slice(0, 3).join(', ') || 'None'}

${contextInfo}

Common Business Entity Relationship Reference:
${this.BUSINESS_KNOWLEDGE.businessRules.map(rule => `- ${rule}`).join('\n')}

Please analyze from a business logic perspective and return validation results in JSON format:
{
  "isBusinessLogical": true/false, // Whether it conforms to business logic
  "businessReasoningScore": 0.0-1.0, // Business reasoning score
  "potentialRisks": ["Risk1", "Risk2"], // Potential business risks
  "recommendations": ["Recommendation1", "Recommendation2"], // Improvement suggestions
  "domainKnowledgeApplied": ["Domain knowledge1", "Domain knowledge2"] // Applied domain knowledge
}

Analysis Points:
1. Evaluate whether the relationship conforms to common business logic patterns
2. Consider data integrity and consistency requirements
3. Identify potential business risks and data quality issues
4. Provide improvement suggestions based on business best practices
5. Consider industry or domain-specific business rules
`;
    }
  }

  /**
   * 构建批量业务验证提示词
   */
  private buildBatchBusinessValidationPrompt(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
    }>,
    businessContext?: any
  ): string {
    const language = this.config.language;
    const relationshipsDescription = relationships.map((item, index) => {
      return `
关联关系 ${index + 1}:
- 源字段: ${item.fromField.tableName}.${item.fromField.name}
- 目标字段: ${item.toField.tableName}.${item.toField.name}
- 关联类型: ${item.relationship.relationshipType}
- 当前置信度: ${item.relationship.confidenceScore}
`;
    }).join('\n');

    if (language === 'zh') {
      return `
请批量验证以下关联关系的业务逻辑合理性：

${relationshipsDescription}

请为每个关联关系返回验证结果，格式如下：
{
  "results": [
    {
      "relationshipIndex": 0,
      "isBusinessLogical": true/false,
      "businessReasoningScore": 0.0-1.0,
      "potentialRisks": ["风险列表"],
      "recommendations": ["建议列表"],
      "domainKnowledgeApplied": ["应用的领域知识"]
    }
    // ... 其他关联关系的结果
  ]
}
`;
    } else {
      return `
Please validate the business logic reasonableness of the following relationships in batch:

${relationshipsDescription}

Please return validation results for each relationship in the following format:
{
  "results": [
    {
      "relationshipIndex": 0,
      "isBusinessLogical": true/false,
      "businessReasoningScore": 0.0-1.0,
      "potentialRisks": ["Risk list"],
      "recommendations": ["Recommendation list"],
      "domainKnowledgeApplied": ["Applied domain knowledge"]
    }
    // ... results for other relationships
  ]
}
`;
    }
  }

  /**
   * 解析业务验证响应
   */
  private parseBusinessValidationResponse(content: string): BusinessValidation {
    try {
      const parsed = JSON.parse(content);
      return {
        isBusinessLogical: parsed.isBusinessLogical || false,
        businessReasoningScore: Math.max(0, Math.min(1, parsed.businessReasoningScore || 0)),
        potentialRisks: Array.isArray(parsed.potentialRisks) ? parsed.potentialRisks : [],
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
        domainKnowledgeApplied: Array.isArray(parsed.domainKnowledgeApplied) ? parsed.domainKnowledgeApplied : []
      };
    } catch (error) {
      console.error('解析业务验证响应失败:', error);
      throw new Error('Invalid LLM business validation response format');
    }
  }

  /**
   * 解析批量业务验证响应
   */
  private parseBatchBusinessValidationResponse(
    content: string,
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
    }>
  ): BusinessValidation[] {
    try {
      const parsed = JSON.parse(content);
      const results = parsed.results || [];
      
      return relationships.map((item, index) => {
        const result = results.find((r: any) => r.relationshipIndex === index);
        if (result) {
          return this.parseBusinessValidationResponse(JSON.stringify(result));
        } else {
          return this.getDefaultBusinessValidation(item.relationship, item.fromField, item.toField);
        }
      });
    } catch (error) {
      console.error('解析批量业务验证响应失败:', error);
      return relationships.map(item => 
        this.getDefaultBusinessValidation(item.relationship, item.fromField, item.toField)
      );
    }
  }

  /**
   * 获取默认业务验证结果
   */
  private getDefaultBusinessValidation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo
  ): BusinessValidation {
    // 基于简单规则的默认验证
    const isLogical = this.simpleBusinessLogicCheck(fromField, toField);
    
    return {
      isBusinessLogical: isLogical,
      businessReasoningScore: isLogical ? 0.6 : 0.3,
      potentialRisks: isLogical ? [] : ['未通过基本业务逻辑检查'],
      recommendations: ['建议人工审核该关联关系'],
      domainKnowledgeApplied: ['基础业务逻辑规则']
    };
  }

  /**
   * 简单的业务逻辑检查
   */
  private simpleBusinessLogicCheck(fromField: FieldInfo, toField: FieldInfo): boolean {
    // 检查是否符合常见的主键-外键模式
    if (fromField.isPrimaryKey && toField.name.toLowerCase().includes(fromField.name.toLowerCase())) {
      return true;
    }
    
    // 检查是否符合常见的业务实体关系
    const fromEntity = this.extractEntityName(fromField.tableName);
    const toEntity = this.extractEntityName(toField.tableName);
    
    return this.BUSINESS_KNOWLEDGE.commonRelationships.some(rel => 
      (rel.from === fromEntity && rel.to === toEntity) ||
      (rel.to === fromEntity && rel.from === toEntity)
    );
  }

  /**
   * 从表名中提取实体名称
   */
  private extractEntityName(tableName: string): string {
    return tableName.toLowerCase()
      .replace(/^(tbl_|t_|tb_)/, '') // 移除表前缀
      .replace(/_?(detail|info|data)$/, '') // 移除常见后缀
      .replace(/s$/, ''); // 移除复数形式
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo
  ): string {
    return `business_${fromField.tableName}.${fromField.name}_${toField.tableName}.${toField.name}_${relationship.relationshipType}`;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.businessRulesCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number } {
    return {
      size: this.businessRulesCache.size
    };
  }
}
