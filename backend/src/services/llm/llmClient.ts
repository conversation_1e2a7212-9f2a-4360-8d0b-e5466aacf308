import { LLMEnhancementConfig } from '../../types';

/**
 * LLM客户端接口
 */
export interface LLMMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LLMChatRequest {
  messages: LLMMessage[];
  temperature?: number;
  max_tokens?: number;
  response_format?: { type: 'json_object' | 'text' };
}

export interface LLMChatResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * 统一的LLM客户端
 * 支持多种LLM提供商
 */
export class LLMClient {
  private config: LLMEnhancementConfig;
  private tokenUsage: { total: number; daily: number } = { total: 0, daily: 0 };

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
  }

  /**
   * 发送聊天请求
   */
  async chat(request: LLMChatRequest): Promise<LLMChatResponse> {
    try {
      switch (this.config.provider) {
        case 'openai':
          return await this.chatWithOpenAI(request);
        case 'azure':
          return await this.chatWithAzure(request);
        case 'anthropic':
          return await this.chatWithAnthropic(request);
        case 'local':
          return await this.chatWithLocal(request);
        default:
          throw new Error(`不支持的LLM提供商: ${this.config.provider}`);
      }
    } catch (error) {
      console.error('LLM请求失败:', error);
      throw error;
    }
  }

  /**
   * OpenAI API调用
   */
  private async chatWithOpenAI(request: LLMChatRequest): Promise<LLMChatResponse> {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY环境变量未设置');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: request.messages,
        temperature: request.temperature || this.config.temperature,
        max_tokens: request.max_tokens || this.config.maxTokens,
        response_format: request.response_format
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`OpenAI API错误: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const usage = data.usage;

    // 记录token使用量
    if (usage) {
      this.tokenUsage.total += usage.total_tokens;
      this.tokenUsage.daily += usage.total_tokens;
    }

    return {
      content: data.choices[0]?.message?.content || '',
      usage
    };
  }

  /**
   * Azure OpenAI API调用
   */
  private async chatWithAzure(request: LLMChatRequest): Promise<LLMChatResponse> {
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;

    if (!apiKey || !endpoint || !deploymentName) {
      throw new Error('Azure OpenAI环境变量未完整设置');
    }

    const url = `${endpoint}/openai/deployments/${deploymentName}/chat/completions?api-version=2024-02-15-preview`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'api-key': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: request.messages,
        temperature: request.temperature || this.config.temperature,
        max_tokens: request.max_tokens || this.config.maxTokens,
        response_format: request.response_format
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Azure OpenAI API错误: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const usage = data.usage;

    if (usage) {
      this.tokenUsage.total += usage.total_tokens;
      this.tokenUsage.daily += usage.total_tokens;
    }

    return {
      content: data.choices[0]?.message?.content || '',
      usage
    };
  }

  /**
   * Anthropic Claude API调用
   */
  private async chatWithAnthropic(request: LLMChatRequest): Promise<LLMChatResponse> {
    const apiKey = process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new Error('ANTHROPIC_API_KEY环境变量未设置');
    }

    // 转换消息格式
    const messages = request.messages.filter(m => m.role !== 'system');
    const systemMessage = request.messages.find(m => m.role === 'system')?.content;

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: request.max_tokens || this.config.maxTokens,
        temperature: request.temperature || this.config.temperature,
        system: systemMessage,
        messages: messages.map(m => ({
          role: m.role === 'assistant' ? 'assistant' : 'user',
          content: m.content
        }))
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Anthropic API错误: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const usage = data.usage;

    if (usage) {
      this.tokenUsage.total += usage.input_tokens + usage.output_tokens;
      this.tokenUsage.daily += usage.input_tokens + usage.output_tokens;
    }

    return {
      content: data.content[0]?.text || '',
      usage: usage ? {
        prompt_tokens: usage.input_tokens,
        completion_tokens: usage.output_tokens,
        total_tokens: usage.input_tokens + usage.output_tokens
      } : undefined
    };
  }

  /**
   * 本地模型API调用
   */
  private async chatWithLocal(request: LLMChatRequest): Promise<LLMChatResponse> {
    const endpoint = process.env.LOCAL_LLM_ENDPOINT || 'http://localhost:8000';
    
    const response = await fetch(`${endpoint}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: request.messages,
        temperature: request.temperature || this.config.temperature,
        max_tokens: request.max_tokens || this.config.maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`本地LLM API错误: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage
    };
  }

  /**
   * 检查token使用限制
   */
  async checkTokenLimit(estimatedTokens: number): Promise<boolean> {
    const dailyLimit = parseInt(process.env.LLM_DAILY_TOKEN_LIMIT || '100000');
    return this.tokenUsage.daily + estimatedTokens <= dailyLimit;
  }

  /**
   * 获取token使用统计
   */
  getTokenUsage(): { total: number; daily: number } {
    return { ...this.tokenUsage };
  }

  /**
   * 重置每日token使用量
   */
  resetDailyTokenUsage(): void {
    this.tokenUsage.daily = 0;
  }

  /**
   * 估算prompt的token数量
   */
  estimateTokens(text: string): number {
    // 简单估算：英文约4字符=1token，中文约1.5字符=1token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const otherChars = text.length - chineseChars;
    return Math.ceil(chineseChars / 1.5 + otherChars / 4);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.chat({
        messages: [{ role: 'user', content: 'Hello' }],
        temperature: 0,
        max_tokens: 10
      });
      return response.content.length > 0;
    } catch (error) {
      console.error('LLM健康检查失败:', error);
      return false;
    }
  }
}
