import {
  LLMEnhancementConfig,
  RelationshipDiscoveryResult,
  FieldInfo,
  SemanticAnalysis,
  BusinessValidation,
  ConfidenceLevel
} from '../../types';
import { LLMClient } from './llmClient';

/**
 * 置信度调整结果接口
 */
export interface ConfidenceAdjustment {
  originalScore: number;
  adjustedScore: number;
  adjustmentReason: string;
  adjustmentFactors: Array<{
    factor: string;
    impact: number; // -1 到 1 之间
    description: string;
  }>;
  finalConfidence: ConfidenceLevel;
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
}

/**
 * 业务上下文接口
 */
export interface BusinessContext {
  industry?: string;
  dataSourceTypes: string[];
  systemType?: string;
  userFeedback?: Array<{
    relationshipId: string;
    isCorrect: boolean;
    feedback: string;
  }>;
  historicalAccuracy?: {
    byMethod: Record<string, number>;
    byConfidenceLevel: Record<string, number>;
  };
  customRules?: string[];
}

/**
 * LLM智能置信度调整器
 * 基于大模型的判断结果动态调整置信度评分
 */
export class LLMConfidenceAdjuster {
  private llmClient: LLMClient;
  private config: LLMEnhancementConfig;
  private adjustmentCache: Map<string, ConfidenceAdjustment> = new Map();

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
    this.llmClient = new LLMClient(config);
  }

  /**
   * 智能调整关联关系的置信度
   */
  async adjustConfidence(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    semanticAnalysis?: SemanticAnalysis,
    businessValidation?: BusinessValidation,
    businessContext?: BusinessContext
  ): Promise<ConfidenceAdjustment> {
    const cacheKey = this.generateCacheKey(relationship, fromField, toField);
    
    // 检查缓存
    if (this.config.enableCache && this.adjustmentCache.has(cacheKey)) {
      return this.adjustmentCache.get(cacheKey)!;
    }

    try {
      const prompt = this.buildConfidenceAdjustmentPrompt(
        relationship,
        fromField,
        toField,
        semanticAnalysis,
        businessValidation,
        businessContext
      );

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      const adjustment = this.parseConfidenceAdjustmentResponse(
        response.content,
        relationship.confidenceScore || 0
      );
      
      // 缓存结果
      if (this.config.enableCache) {
        this.adjustmentCache.set(cacheKey, adjustment);
      }

      return adjustment;
    } catch (error) {
      console.error('LLM置信度调整失败:', error);
      return this.getDefaultConfidenceAdjustment(relationship.confidenceScore || 0);
    }
  }

  /**
   * 批量调整多个关联关系的置信度
   */
  async adjustBatchConfidence(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
      semanticAnalysis?: SemanticAnalysis;
      businessValidation?: BusinessValidation;
    }>,
    businessContext?: BusinessContext
  ): Promise<ConfidenceAdjustment[]> {
    const batchSize = this.config.batchSize;
    const results: ConfidenceAdjustment[] = [];

    for (let i = 0; i < relationships.length; i += batchSize) {
      const batch = relationships.slice(i, i + batchSize);
      const batchPrompt = this.buildBatchConfidenceAdjustmentPrompt(batch, businessContext);

      try {
        const response = await this.llmClient.chat({
          messages: [{ role: 'user', content: batchPrompt }],
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens * batch.length,
          response_format: { type: 'json_object' }
        });

        const batchResults = this.parseBatchConfidenceAdjustmentResponse(response.content, batch);
        results.push(...batchResults);
      } catch (error) {
        console.error('批量LLM置信度调整失败:', error);
        const defaultResults = batch.map(item => 
          this.getDefaultConfidenceAdjustment(item.relationship.confidenceScore || 0)
        );
        results.push(...defaultResults);
      }
    }

    return results;
  }

  /**
   * 基于历史反馈学习调整策略
   */
  async learnFromFeedback(
    feedbackData: Array<{
      relationship: RelationshipDiscoveryResult;
      originalConfidence: number;
      userFeedback: {
        isCorrect: boolean;
        actualRelationType?: string;
        comments?: string;
      };
    }>
  ): Promise<{
    learningInsights: string[];
    adjustmentRules: Array<{
      condition: string;
      adjustment: number;
      reason: string;
    }>;
    recommendedThresholds: {
      autoApply: number;
      manualReview: number;
      reject: number;
    };
  }> {
    try {
      const prompt = this.buildFeedbackLearningPrompt(feedbackData);

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      return this.parseFeedbackLearningResponse(response.content);
    } catch (error) {
      console.error('从反馈学习失败:', error);
      return {
        learningInsights: ['无法从反馈中学习'],
        adjustmentRules: [],
        recommendedThresholds: {
          autoApply: 0.8,
          manualReview: 0.5,
          reject: 0.3
        }
      };
    }
  }

  /**
   * 构建置信度调整提示词
   */
  private buildConfidenceAdjustmentPrompt(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    semanticAnalysis?: SemanticAnalysis,
    businessValidation?: BusinessValidation,
    businessContext?: BusinessContext
  ): string {
    const language = this.config.language;

    const contextInfo = businessContext ? `
业务上下文：
- 行业领域: ${businessContext.industry || '未知'}
- 数据源类型: ${businessContext.dataSourceTypes.join(', ')}
- 系统类型: ${businessContext.systemType || '未知'}
- 历史准确率: ${JSON.stringify(businessContext.historicalAccuracy) || '无'}
` : '';

    const semanticInfo = semanticAnalysis ? `
语义分析结果：
- 语义相似性: ${semanticAnalysis.semanticSimilarity}
- 业务关联性: ${semanticAnalysis.businessRelevance}
- 数据类型兼容性: ${semanticAnalysis.dataTypeCompatibility}
- 分析置信度: ${semanticAnalysis.confidence}
` : '';

    const businessInfo = businessValidation ? `
业务验证结果：
- 业务逻辑合理性: ${businessValidation.isBusinessLogical}
- 业务推理分数: ${businessValidation.businessReasoningScore}
- 潜在风险: ${businessValidation.potentialRisks.join(', ')}
` : '';

    if (language === 'zh') {
      return `
作为数据关系置信度评估专家，请基于多维度信息调整以下关联关系的置信度：

原始关联关系信息：
- 源字段: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- 目标字段: ${toField.tableName}.${toField.name} (${toField.dataType})
- 关联类型: ${relationship.relationshipType}
- 发现方法: ${relationship.discoveryMethod}
- 原始置信度: ${relationship.confidenceScore}
- 原始证据: ${relationship.evidence?.join(', ') || '无'}

${contextInfo}
${semanticInfo}
${businessInfo}

请综合考虑以下因素进行置信度调整：
1. 发现方法的可靠性
2. 语义分析的结果
3. 业务逻辑的合理性
4. 历史数据的准确性
5. 数据质量和完整性
6. 行业特定的规则和模式

请以JSON格式返回调整结果：
{
  "adjustedScore": 0.0-1.0, // 调整后的置信度分数
  "adjustmentReason": "调整的主要原因说明",
  "adjustmentFactors": [
    {
      "factor": "调整因子名称",
      "impact": -1.0到1.0, // 对置信度的影响程度
      "description": "该因子的具体影响说明"
    }
  ],
  "finalConfidence": "high|medium|low", // 最终置信度等级
  "riskAssessment": {
    "level": "low|medium|high", // 风险等级
    "factors": ["风险因素列表"],
    "mitigation": ["风险缓解建议"]
  }
}

调整原则：
- 外键约束发现的关系应该有较高置信度
- 语义分析和业务验证结果一致时提高置信度
- 存在业务逻辑冲突时降低置信度
- 考虑历史反馈的准确性模式
- 数据质量问题会影响置信度
`;
    } else {
      return `
As a data relationship confidence assessment expert, please adjust the confidence of the following relationship based on multi-dimensional information:

Original Relationship Information:
- Source Field: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- Target Field: ${toField.tableName}.${toField.name} (${toField.dataType})
- Relationship Type: ${relationship.relationshipType}
- Discovery Method: ${relationship.discoveryMethod}
- Original Confidence: ${relationship.confidenceScore}
- Original Evidence: ${relationship.evidence?.join(', ') || 'None'}

${contextInfo}
${semanticInfo}
${businessInfo}

Please consider the following factors for confidence adjustment:
1. Reliability of discovery method
2. Semantic analysis results
3. Business logic reasonableness
4. Historical data accuracy
5. Data quality and completeness
6. Industry-specific rules and patterns

Please return adjustment results in JSON format:
{
  "adjustedScore": 0.0-1.0, // Adjusted confidence score
  "adjustmentReason": "Main reason for adjustment",
  "adjustmentFactors": [
    {
      "factor": "Adjustment factor name",
      "impact": -1.0 to 1.0, // Impact on confidence
      "description": "Specific impact description"
    }
  ],
  "finalConfidence": "high|medium|low", // Final confidence level
  "riskAssessment": {
    "level": "low|medium|high", // Risk level
    "factors": ["Risk factor list"],
    "mitigation": ["Risk mitigation suggestions"]
  }
}

Adjustment Principles:
- Foreign key discovered relationships should have higher confidence
- Increase confidence when semantic analysis and business validation align
- Decrease confidence when business logic conflicts exist
- Consider historical feedback accuracy patterns
- Data quality issues affect confidence
`;
    }
  }

  /**
   * 构建批量置信度调整提示词
   */
  private buildBatchConfidenceAdjustmentPrompt(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
      semanticAnalysis?: SemanticAnalysis;
      businessValidation?: BusinessValidation;
    }>,
    businessContext?: BusinessContext
  ): string {
    const language = this.config.language;
    const relationshipsDescription = relationships.map((item, index) => {
      return `
关联关系 ${index + 1}:
- 源字段: ${item.fromField.tableName}.${item.fromField.name}
- 目标字段: ${item.toField.tableName}.${item.toField.name}
- 原始置信度: ${item.relationship.confidenceScore}
- 发现方法: ${item.relationship.discoveryMethod}
- 语义相似性: ${item.semanticAnalysis?.semanticSimilarity || '未知'}
- 业务合理性: ${item.businessValidation?.isBusinessLogical || '未知'}
`;
    }).join('\n');

    if (language === 'zh') {
      return `
请批量调整以下关联关系的置信度：

${relationshipsDescription}

请为每个关联关系返回调整结果，格式如下：
{
  "results": [
    {
      "relationshipIndex": 0,
      "adjustedScore": 0.0-1.0,
      "adjustmentReason": "调整原因",
      "adjustmentFactors": [{"factor": "因子", "impact": 0.0, "description": "说明"}],
      "finalConfidence": "high|medium|low",
      "riskAssessment": {
        "level": "low|medium|high",
        "factors": ["风险因素"],
        "mitigation": ["缓解建议"]
      }
    }
    // ... 其他关联关系的调整结果
  ]
}
`;
    } else {
      return `
Please adjust the confidence of the following relationships in batch:

${relationshipsDescription}

Please return adjustment results for each relationship in the following format:
{
  "results": [
    {
      "relationshipIndex": 0,
      "adjustedScore": 0.0-1.0,
      "adjustmentReason": "Adjustment reason",
      "adjustmentFactors": [{"factor": "Factor", "impact": 0.0, "description": "Description"}],
      "finalConfidence": "high|medium|low",
      "riskAssessment": {
        "level": "low|medium|high",
        "factors": ["Risk factors"],
        "mitigation": ["Mitigation suggestions"]
      }
    }
    // ... adjustment results for other relationships
  ]
}
`;
    }
  }

  /**
   * 构建反馈学习提示词
   */
  private buildFeedbackLearningPrompt(
    feedbackData: Array<{
      relationship: RelationshipDiscoveryResult;
      originalConfidence: number;
      userFeedback: any;
    }>
  ): string {
    const language = this.config.language;
    const feedbackSummary = feedbackData.map((item, index) => {
      return `
反馈 ${index + 1}:
- 发现方法: ${item.relationship.discoveryMethod}
- 原始置信度: ${item.originalConfidence}
- 用户反馈: ${item.userFeedback.isCorrect ? '正确' : '错误'}
- 评论: ${item.userFeedback.comments || '无'}
`;
    }).join('\n');

    if (language === 'zh') {
      return `
基于以下用户反馈数据，分析置信度评估的准确性并提出改进建议：

${feedbackSummary}

请分析并返回学习结果：
{
  "learningInsights": ["从反馈中学到的关键洞察"],
  "adjustmentRules": [
    {
      "condition": "触发条件",
      "adjustment": -0.5到0.5, // 置信度调整幅度
      "reason": "调整原因"
    }
  ],
  "recommendedThresholds": {
    "autoApply": 0.0-1.0, // 自动应用阈值
    "manualReview": 0.0-1.0, // 人工审核阈值
    "reject": 0.0-1.0 // 拒绝阈值
  }
}
`;
    } else {
      return `
Based on the following user feedback data, analyze the accuracy of confidence assessment and provide improvement suggestions:

${feedbackSummary}

Please analyze and return learning results:
{
  "learningInsights": ["Key insights learned from feedback"],
  "adjustmentRules": [
    {
      "condition": "Trigger condition",
      "adjustment": -0.5 to 0.5, // Confidence adjustment magnitude
      "reason": "Adjustment reason"
    }
  ],
  "recommendedThresholds": {
    "autoApply": 0.0-1.0, // Auto-apply threshold
    "manualReview": 0.0-1.0, // Manual review threshold
    "reject": 0.0-1.0 // Rejection threshold
  }
}
`;
    }
  }

  /**
   * 解析置信度调整响应
   */
  private parseConfidenceAdjustmentResponse(
    content: string,
    originalScore: number
  ): ConfidenceAdjustment {
    try {
      const parsed = JSON.parse(content);
      const adjustedScore = Math.max(0, Math.min(1, parsed.adjustedScore || originalScore));
      
      return {
        originalScore,
        adjustedScore,
        adjustmentReason: parsed.adjustmentReason || '无调整原因',
        adjustmentFactors: Array.isArray(parsed.adjustmentFactors) ? parsed.adjustmentFactors : [],
        finalConfidence: parsed.finalConfidence || this.scoreToConfidenceLevel(adjustedScore),
        riskAssessment: {
          level: parsed.riskAssessment?.level || 'medium',
          factors: Array.isArray(parsed.riskAssessment?.factors) ? parsed.riskAssessment.factors : [],
          mitigation: Array.isArray(parsed.riskAssessment?.mitigation) ? parsed.riskAssessment.mitigation : []
        }
      };
    } catch (error) {
      console.error('解析置信度调整响应失败:', error);
      throw new Error('Invalid confidence adjustment response format');
    }
  }

  /**
   * 解析批量置信度调整响应
   */
  private parseBatchConfidenceAdjustmentResponse(
    content: string,
    relationships: Array<any>
  ): ConfidenceAdjustment[] {
    try {
      const parsed = JSON.parse(content);
      const results = parsed.results || [];
      
      return relationships.map((item, index) => {
        const result = results.find((r: any) => r.relationshipIndex === index);
        if (result) {
          return this.parseConfidenceAdjustmentResponse(
            JSON.stringify(result),
            item.relationship.confidenceScore || 0
          );
        } else {
          return this.getDefaultConfidenceAdjustment(item.relationship.confidenceScore || 0);
        }
      });
    } catch (error) {
      console.error('解析批量置信度调整响应失败:', error);
      return relationships.map(item => 
        this.getDefaultConfidenceAdjustment(item.relationship.confidenceScore || 0)
      );
    }
  }

  /**
   * 解析反馈学习响应
   */
  private parseFeedbackLearningResponse(content: string): any {
    try {
      const parsed = JSON.parse(content);
      return {
        learningInsights: Array.isArray(parsed.learningInsights) ? parsed.learningInsights : [],
        adjustmentRules: Array.isArray(parsed.adjustmentRules) ? parsed.adjustmentRules : [],
        recommendedThresholds: parsed.recommendedThresholds || {
          autoApply: 0.8,
          manualReview: 0.5,
          reject: 0.3
        }
      };
    } catch (error) {
      console.error('解析反馈学习响应失败:', error);
      throw new Error('Invalid feedback learning response format');
    }
  }

  /**
   * 获取默认置信度调整
   */
  private getDefaultConfidenceAdjustment(originalScore: number): ConfidenceAdjustment {
    return {
      originalScore,
      adjustedScore: originalScore,
      adjustmentReason: 'LLM调整失败，保持原始置信度',
      adjustmentFactors: [],
      finalConfidence: this.scoreToConfidenceLevel(originalScore),
      riskAssessment: {
        level: 'medium',
        factors: ['LLM调整失败'],
        mitigation: ['建议人工审核']
      }
    };
  }

  /**
   * 将分数转换为置信度等级
   */
  private scoreToConfidenceLevel(score: number): ConfidenceLevel {
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo
  ): string {
    return `confidence_${fromField.tableName}.${fromField.name}_${toField.tableName}.${toField.name}_${relationship.discoveryMethod}_${relationship.confidenceScore}`;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.adjustmentCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number } {
    return {
      size: this.adjustmentCache.size
    };
  }
}
