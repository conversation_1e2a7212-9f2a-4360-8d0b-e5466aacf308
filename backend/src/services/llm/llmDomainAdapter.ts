import {
  LLMEnhancementConfig,
  FieldInfo,
  SemanticAnalysis
} from '../../types';
import { LLMClient } from './llmClient';

/**
 * 领域知识库接口
 */
export interface DomainKnowledge {
  industry: string;
  entities: string[];
  relationships: Array<{
    from: string;
    to: string;
    type: string;
    confidence: number;
  }>;
  terminology: Record<string, string[]>; // 术语及其同义词
  businessRules: string[];
  commonPatterns: string[];
}

/**
 * LLM领域适配器
 * 支持中英文混合、特定行业术语的关联关系识别
 */
export class LLMDomainAdapter {
  private llmClient: LLMClient;
  private config: LLMEnhancementConfig;
  private domainKnowledgeCache: Map<string, DomainKnowledge> = new Map();

  // 预定义的行业领域知识库
  private readonly DOMAIN_KNOWLEDGE: Record<string, DomainKnowledge> = {
    'finance': {
      industry: '金融',
      entities: ['account', 'transaction', 'customer', 'loan', 'deposit', 'credit', 'debit'],
      relationships: [
        { from: 'customer', to: 'account', type: 'one_to_many', confidence: 0.95 },
        { from: 'account', to: 'transaction', type: 'one_to_many', confidence: 0.9 }
      ],
      terminology: {
        'account': ['账户', '帐户', 'acct'],
        'transaction': ['交易', '事务', 'txn', 'trans'],
        'customer': ['客户', '用户', 'cust', 'client'],
        'balance': ['余额', '结余', 'bal'],
        'amount': ['金额', '数额', 'amt']
      },
      businessRules: [
        '每个客户可以有多个账户',
        '每个账户可以有多笔交易',
        '交易必须关联到特定账户'
      ],
      commonPatterns: ['customer_id', 'account_number', 'transaction_id']
    },
    'ecommerce': {
      industry: '电商',
      entities: ['customer', 'order', 'product', 'category', 'inventory', 'payment'],
      relationships: [
        { from: 'customer', to: 'order', type: 'one_to_many', confidence: 0.95 },
        { from: 'order', to: 'order_item', type: 'one_to_many', confidence: 0.9 },
        { from: 'product', to: 'order_item', type: 'one_to_many', confidence: 0.85 }
      ],
      terminology: {
        'customer': ['客户', '用户', '买家', 'buyer', 'user'],
        'order': ['订单', '单据', 'ord'],
        'product': ['产品', '商品', 'item', 'goods', 'prod'],
        'category': ['分类', '类别', 'cat', 'type'],
        'price': ['价格', '单价', '金额', 'amount']
      },
      businessRules: [
        '客户可以下多个订单',
        '订单包含多个商品',
        '商品属于特定分类'
      ],
      commonPatterns: ['customer_id', 'order_id', 'product_id', 'sku']
    },
    'healthcare': {
      industry: '医疗',
      entities: ['patient', 'doctor', 'appointment', 'diagnosis', 'treatment', 'medication'],
      relationships: [
        { from: 'patient', to: 'appointment', type: 'one_to_many', confidence: 0.9 },
        { from: 'doctor', to: 'appointment', type: 'one_to_many', confidence: 0.9 }
      ],
      terminology: {
        'patient': ['患者', '病人', '病患'],
        'doctor': ['医生', '医师', '大夫', 'physician'],
        'appointment': ['预约', '挂号', '就诊'],
        'diagnosis': ['诊断', '病症'],
        'treatment': ['治疗', '疗法']
      },
      businessRules: [
        '患者可以有多次预约',
        '医生可以接诊多个患者',
        '每次预约对应一个诊断'
      ],
      commonPatterns: ['patient_id', 'doctor_id', 'appointment_id']
    }
  };

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
    this.llmClient = new LLMClient(config);
  }

  /**
   * 基于领域知识增强语义分析
   */
  async enhanceSemanticAnalysisWithDomain(
    field1: FieldInfo,
    field2: FieldInfo,
    domain: string = 'general'
  ): Promise<SemanticAnalysis> {
    try {
      const domainKnowledge = await this.getDomainKnowledge(domain);
      const prompt = this.buildDomainEnhancedPrompt(field1, field2, domainKnowledge);

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      return this.parseEnhancedSemanticResponse(response.content);
    } catch (error) {
      console.error('领域增强语义分析失败:', error);
      // 降级到基础分析
      return this.getBasicSemanticAnalysis(field1, field2);
    }
  }

  /**
   * 多语言术语标准化
   */
  async normalizeMultilingualTerms(
    fieldName: string,
    domain: string = 'general'
  ): Promise<{
    normalizedName: string;
    synonyms: string[];
    language: 'zh' | 'en' | 'mixed';
    confidence: number;
  }> {
    try {
      const domainKnowledge = await this.getDomainKnowledge(domain);
      const prompt = this.buildTermNormalizationPrompt(fieldName, domainKnowledge);

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1, // 低温度确保一致性
        max_tokens: 200,
        response_format: { type: 'json_object' }
      });

      return this.parseTermNormalizationResponse(response.content);
    } catch (error) {
      console.error('多语言术语标准化失败:', error);
      return {
        normalizedName: fieldName,
        synonyms: [],
        language: this.detectLanguage(fieldName),
        confidence: 0.1
      };
    }
  }

  /**
   * 检测字段名称中的行业特定模式
   */
  async detectIndustryPatterns(
    fieldName: string,
    tableName: string,
    sampleData?: any[]
  ): Promise<{
    detectedDomain: string;
    confidence: number;
    patterns: string[];
    suggestions: string[];
  }> {
    try {
      const prompt = this.buildPatternDetectionPrompt(fieldName, tableName, sampleData);

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      return this.parsePatternDetectionResponse(response.content);
    } catch (error) {
      console.error('行业模式检测失败:', error);
      return {
        detectedDomain: 'general',
        confidence: 0.1,
        patterns: [],
        suggestions: []
      };
    }
  }

  /**
   * 获取领域知识
   */
  private async getDomainKnowledge(domain: string): Promise<DomainKnowledge> {
    if (this.domainKnowledgeCache.has(domain)) {
      return this.domainKnowledgeCache.get(domain)!;
    }

    // 如果有预定义的领域知识，直接返回
    if (this.DOMAIN_KNOWLEDGE[domain]) {
      const knowledge = this.DOMAIN_KNOWLEDGE[domain];
      this.domainKnowledgeCache.set(domain, knowledge);
      return knowledge;
    }

    // 否则使用LLM生成领域知识
    try {
      const generatedKnowledge = await this.generateDomainKnowledge(domain);
      this.domainKnowledgeCache.set(domain, generatedKnowledge);
      return generatedKnowledge;
    } catch (error) {
      console.error('生成领域知识失败:', error);
      // 返回通用领域知识
      return this.getGeneralDomainKnowledge();
    }
  }

  /**
   * 构建领域增强提示词
   */
  private buildDomainEnhancedPrompt(
    field1: FieldInfo,
    field2: FieldInfo,
    domainKnowledge: DomainKnowledge
  ): string {
    const language = this.config.language;

    if (language === 'zh') {
      return `
作为${domainKnowledge.industry}领域的数据关系分析专家，请分析以下两个字段的关联关系：

字段1: ${field1.tableName}.${field1.name} (${field1.dataType})
字段2: ${field2.tableName}.${field2.name} (${field2.dataType})

领域知识参考：
行业: ${domainKnowledge.industry}
常见实体: ${domainKnowledge.entities.join(', ')}
术语词典: ${JSON.stringify(domainKnowledge.terminology, null, 2)}
业务规则: ${domainKnowledge.businessRules.join('; ')}
常见模式: ${domainKnowledge.commonPatterns.join(', ')}

请特别注意：
1. 中英文混合字段名的处理
2. 行业特定术语的识别
3. 领域业务逻辑的应用
4. 多语言同义词的匹配

以JSON格式返回分析结果：
{
  "semanticSimilarity": 0.0-1.0,
  "businessRelevance": 0.0-1.0,
  "dataTypeCompatibility": 0.0-1.0,
  "suggestedRelationType": "one_to_one|one_to_many|many_to_one",
  "explanation": "详细解释，包括领域知识的应用",
  "confidence": "high|medium|low",
  "domainSpecificFactors": ["领域特定因素1", "领域特定因素2"],
  "multilingualMatches": ["多语言匹配信息"]
}
`;
    } else {
      return `
As a data relationship analysis expert in the ${domainKnowledge.industry} domain, please analyze the relationship between the following two fields:

Field 1: ${field1.tableName}.${field1.name} (${field1.dataType})
Field 2: ${field2.tableName}.${field2.name} (${field2.dataType})

Domain Knowledge Reference:
Industry: ${domainKnowledge.industry}
Common Entities: ${domainKnowledge.entities.join(', ')}
Terminology: ${JSON.stringify(domainKnowledge.terminology, null, 2)}
Business Rules: ${domainKnowledge.businessRules.join('; ')}
Common Patterns: ${domainKnowledge.commonPatterns.join(', ')}

Please pay special attention to:
1. Handling of mixed Chinese-English field names
2. Recognition of industry-specific terminology
3. Application of domain business logic
4. Multilingual synonym matching

Return analysis results in JSON format:
{
  "semanticSimilarity": 0.0-1.0,
  "businessRelevance": 0.0-1.0,
  "dataTypeCompatibility": 0.0-1.0,
  "suggestedRelationType": "one_to_one|one_to_many|many_to_one",
  "explanation": "Detailed explanation including domain knowledge application",
  "confidence": "high|medium|low",
  "domainSpecificFactors": ["Domain factor 1", "Domain factor 2"],
  "multilingualMatches": ["Multilingual match information"]
}
`;
    }
  }

  /**
   * 构建术语标准化提示词
   */
  private buildTermNormalizationPrompt(
    fieldName: string,
    domainKnowledge: DomainKnowledge
  ): string {
    return `
请标准化以下字段名称，考虑中英文混合和行业术语：

字段名称: ${fieldName}
行业领域: ${domainKnowledge.industry}
术语词典: ${JSON.stringify(domainKnowledge.terminology, null, 2)}

请返回JSON格式结果：
{
  "normalizedName": "标准化后的名称",
  "synonyms": ["同义词列表"],
  "language": "zh|en|mixed",
  "confidence": 0.0-1.0
}
`;
  }

  /**
   * 构建模式检测提示词
   */
  private buildPatternDetectionPrompt(
    fieldName: string,
    tableName: string,
    sampleData?: any[]
  ): string {
    const sampleDataStr = sampleData ? sampleData.slice(0, 5).join(', ') : '无';
    
    return `
请检测以下字段的行业特定模式：

字段名称: ${fieldName}
表名: ${tableName}
样本数据: ${sampleDataStr}

已知行业领域: ${Object.keys(this.DOMAIN_KNOWLEDGE).join(', ')}

请返回JSON格式结果：
{
  "detectedDomain": "检测到的领域",
  "confidence": 0.0-1.0,
  "patterns": ["检测到的模式"],
  "suggestions": ["改进建议"]
}
`;
  }

  /**
   * 解析增强语义响应
   */
  private parseEnhancedSemanticResponse(content: string): SemanticAnalysis {
    try {
      const parsed = JSON.parse(content);
      return {
        semanticSimilarity: Math.max(0, Math.min(1, parsed.semanticSimilarity || 0)),
        businessRelevance: Math.max(0, Math.min(1, parsed.businessRelevance || 0)),
        dataTypeCompatibility: Math.max(0, Math.min(1, parsed.dataTypeCompatibility || 0)),
        suggestedRelationType: parsed.suggestedRelationType || 'many_to_one',
        explanation: parsed.explanation || '无法生成解释',
        confidence: parsed.confidence || 'low'
      };
    } catch (error) {
      throw new Error('Invalid enhanced semantic response format');
    }
  }

  /**
   * 解析术语标准化响应
   */
  private parseTermNormalizationResponse(content: string): {
    normalizedName: string;
    synonyms: string[];
    language: 'zh' | 'en' | 'mixed';
    confidence: number;
  } {
    try {
      const parsed = JSON.parse(content);
      return {
        normalizedName: parsed.normalizedName || '',
        synonyms: Array.isArray(parsed.synonyms) ? parsed.synonyms : [],
        language: parsed.language || 'mixed',
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0))
      };
    } catch (error) {
      throw new Error('Invalid term normalization response format');
    }
  }

  /**
   * 解析模式检测响应
   */
  private parsePatternDetectionResponse(content: string): {
    detectedDomain: string;
    confidence: number;
    patterns: string[];
    suggestions: string[];
  } {
    try {
      const parsed = JSON.parse(content);
      return {
        detectedDomain: parsed.detectedDomain || 'general',
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0)),
        patterns: Array.isArray(parsed.patterns) ? parsed.patterns : [],
        suggestions: Array.isArray(parsed.suggestions) ? parsed.suggestions : []
      };
    } catch (error) {
      throw new Error('Invalid pattern detection response format');
    }
  }

  /**
   * 生成领域知识
   */
  private async generateDomainKnowledge(domain: string): Promise<DomainKnowledge> {
    const prompt = `
请为"${domain}"领域生成数据关系分析所需的领域知识，包括：
1. 常见实体类型
2. 典型关联关系
3. 术语词典（中英文对照）
4. 业务规则
5. 常见字段命名模式

请以JSON格式返回。
`;

    const response = await this.llmClient.chat({
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.3,
      max_tokens: 1000,
      response_format: { type: 'json_object' }
    });

    return JSON.parse(response.content);
  }

  /**
   * 获取通用领域知识
   */
  private getGeneralDomainKnowledge(): DomainKnowledge {
    return {
      industry: '通用',
      entities: ['user', 'data', 'record', 'item', 'info'],
      relationships: [],
      terminology: {
        'id': ['标识', '编号', 'identifier'],
        'name': ['名称', '姓名', 'title'],
        'date': ['日期', '时间', 'time'],
        'status': ['状态', '状况', 'state']
      },
      businessRules: ['基础数据完整性规则'],
      commonPatterns: ['id', 'name', 'date', 'status']
    };
  }

  /**
   * 获取基础语义分析
   */
  private getBasicSemanticAnalysis(field1: FieldInfo, field2: FieldInfo): SemanticAnalysis {
    return {
      semanticSimilarity: 0.1,
      businessRelevance: 0.1,
      dataTypeCompatibility: field1.dataType === field2.dataType ? 0.8 : 0.2,
      suggestedRelationType: 'many_to_one',
      explanation: '领域增强分析失败，使用基础分析',
      confidence: 'low'
    };
  }

  /**
   * 检测语言类型
   */
  private detectLanguage(text: string): 'zh' | 'en' | 'mixed' {
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
    
    if (chineseChars > 0 && englishChars > 0) return 'mixed';
    if (chineseChars > 0) return 'zh';
    return 'en';
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.domainKnowledgeCache.clear();
  }
}
