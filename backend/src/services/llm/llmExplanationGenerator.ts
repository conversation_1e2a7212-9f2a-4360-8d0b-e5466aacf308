import {
  LLMEnhancementConfig,
  RelationshipDiscoveryResult,
  FieldInfo,
  SemanticAnalysis,
  BusinessValidation
} from '../../types';
import { LLMClient } from './llmClient';

/**
 * 解释生成结果接口
 */
export interface RelationshipExplanation {
  summary: string;           // 简要说明
  detailedExplanation: string; // 详细解释
  businessContext: string;   // 业务上下文
  technicalDetails: string;  // 技术细节
  confidence: string;        // 置信度说明
  recommendations: string[]; // 建议
  language: 'zh' | 'en';    // 语言
}

/**
 * LLM关联关系解释生成器
 * 使用大模型生成关联关系的自然语言解释，帮助用户理解
 */
export class LLMExplanationGenerator {
  private llmClient: LLMClient;
  private config: LLMEnhancementConfig;
  private explanationCache: Map<string, RelationshipExplanation> = new Map();

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
    this.llmClient = new LLMClient(config);
  }

  /**
   * 生成关联关系的完整解释
   */
  async generateComprehensiveExplanation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    semanticAnalysis?: SemanticAnalysis,
    businessValidation?: BusinessValidation,
    userLevel: 'technical' | 'business' | 'general' = 'general'
  ): Promise<RelationshipExplanation> {
    const cacheKey = this.generateCacheKey(relationship, fromField, toField, userLevel);
    
    // 检查缓存
    if (this.config.enableCache && this.explanationCache.has(cacheKey)) {
      return this.explanationCache.get(cacheKey)!;
    }

    try {
      const prompt = this.buildComprehensiveExplanationPrompt(
        relationship,
        fromField,
        toField,
        semanticAnalysis,
        businessValidation,
        userLevel
      );

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      const explanation = this.parseExplanationResponse(response.content);
      
      // 缓存结果
      if (this.config.enableCache) {
        this.explanationCache.set(cacheKey, explanation);
      }

      return explanation;
    } catch (error) {
      console.error('LLM解释生成失败:', error);
      return this.getDefaultExplanation(relationship, fromField, toField, userLevel);
    }
  }

  /**
   * 生成简要解释
   */
  async generateBriefExplanation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    maxLength: number = 100
  ): Promise<string> {
    try {
      const prompt = this.buildBriefExplanationPrompt(
        relationship,
        fromField,
        toField,
        maxLength
      );

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: Math.min(200, this.config.maxTokens)
      });

      return response.content.trim();
    } catch (error) {
      console.error('简要解释生成失败:', error);
      return this.getDefaultBriefExplanation(relationship, fromField, toField);
    }
  }

  /**
   * 批量生成解释
   */
  async generateBatchExplanations(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
      semanticAnalysis?: SemanticAnalysis;
      businessValidation?: BusinessValidation;
    }>,
    userLevel: 'technical' | 'business' | 'general' = 'general'
  ): Promise<RelationshipExplanation[]> {
    const batchSize = this.config.batchSize;
    const results: RelationshipExplanation[] = [];

    for (let i = 0; i < relationships.length; i += batchSize) {
      const batch = relationships.slice(i, i + batchSize);
      const batchPrompt = this.buildBatchExplanationPrompt(batch, userLevel);

      try {
        const response = await this.llmClient.chat({
          messages: [{ role: 'user', content: batchPrompt }],
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens * batch.length,
          response_format: { type: 'json_object' }
        });

        const batchResults = this.parseBatchExplanationResponse(response.content, batch);
        results.push(...batchResults);
      } catch (error) {
        console.error('批量解释生成失败:', error);
        const defaultResults = batch.map(item => 
          this.getDefaultExplanation(item.relationship, item.fromField, item.toField, userLevel)
        );
        results.push(...defaultResults);
      }
    }

    return results;
  }

  /**
   * 生成个性化解释
   */
  async generatePersonalizedExplanation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    userProfile: {
      role: string;
      experience: 'beginner' | 'intermediate' | 'expert';
      domain: string;
      language: 'zh' | 'en';
      preferences: string[];
    }
  ): Promise<RelationshipExplanation> {
    try {
      const prompt = this.buildPersonalizedExplanationPrompt(
        relationship,
        fromField,
        toField,
        userProfile
      );

      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      return this.parseExplanationResponse(response.content);
    } catch (error) {
      console.error('个性化解释生成失败:', error);
      return this.getDefaultExplanation(relationship, fromField, toField, 'general');
    }
  }

  /**
   * 构建综合解释提示词
   */
  private buildComprehensiveExplanationPrompt(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    semanticAnalysis?: SemanticAnalysis,
    businessValidation?: BusinessValidation,
    userLevel: string = 'general'
  ): string {
    const language = this.config.language;
    const levelDescription = this.getUserLevelDescription(userLevel, language);

    const semanticInfo = semanticAnalysis ? `
语义分析结果：
- 语义相似性: ${semanticAnalysis.semanticSimilarity}
- 业务关联性: ${semanticAnalysis.businessRelevance}
- 数据类型兼容性: ${semanticAnalysis.dataTypeCompatibility}
- 分析说明: ${semanticAnalysis.explanation}
` : '';

    const businessInfo = businessValidation ? `
业务验证结果：
- 业务逻辑合理性: ${businessValidation.isBusinessLogical ? '合理' : '不合理'}
- 业务推理分数: ${businessValidation.businessReasoningScore}
- 潜在风险: ${businessValidation.potentialRisks.join(', ')}
- 改进建议: ${businessValidation.recommendations.join(', ')}
` : '';

    if (language === 'zh') {
      return `
请为以下数据关联关系生成${levelDescription}的详细解释：

关联关系信息：
- 源字段: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- 目标字段: ${toField.tableName}.${toField.name} (${toField.dataType})
- 关联类型: ${relationship.relationshipType}
- 发现方法: ${relationship.discoveryMethod}
- 置信度: ${relationship.confidence} (${relationship.confidenceScore})
- 证据: ${relationship.evidence?.join(', ') || '无'}

${semanticInfo}
${businessInfo}

字段详细信息：
源字段 - ${fromField.name}:
- 描述: ${fromField.description || '无'}
- 是否主键: ${fromField.isPrimaryKey ? '是' : '否'}
- 样本数据: ${fromField.sampleData?.slice(0, 3).join(', ') || '无'}

目标字段 - ${toField.name}:
- 描述: ${toField.description || '无'}
- 是否主键: ${toField.isPrimaryKey ? '是' : '否'}
- 样本数据: ${toField.sampleData?.slice(0, 3).join(', ') || '无'}

请以JSON格式返回解释：
{
  "summary": "一句话简要说明这个关联关系",
  "detailedExplanation": "详细解释为什么这两个字段相关，包括技术和业务角度",
  "businessContext": "从业务角度解释这个关联关系的意义和作用",
  "technicalDetails": "技术实现细节和注意事项",
  "confidence": "置信度的通俗解释，说明为什么是这个置信度",
  "recommendations": ["针对用户的具体建议"],
  "language": "zh"
}

要求：
1. 语言通俗易懂，适合${levelDescription}理解
2. 避免过多技术术语
3. 重点说明业务价值和实际应用
4. 如果置信度较低，要说明原因和风险
`;
    } else {
      return `
Please generate a detailed explanation for the following data relationship suitable for ${levelDescription}:

Relationship Information:
- Source Field: ${fromField.tableName}.${fromField.name} (${fromField.dataType})
- Target Field: ${toField.tableName}.${toField.name} (${toField.dataType})
- Relationship Type: ${relationship.relationshipType}
- Discovery Method: ${relationship.discoveryMethod}
- Confidence: ${relationship.confidence} (${relationship.confidenceScore})
- Evidence: ${relationship.evidence?.join(', ') || 'None'}

${semanticInfo}
${businessInfo}

Field Details:
Source Field - ${fromField.name}:
- Description: ${fromField.description || 'None'}
- Is Primary Key: ${fromField.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${fromField.sampleData?.slice(0, 3).join(', ') || 'None'}

Target Field - ${toField.name}:
- Description: ${toField.description || 'None'}
- Is Primary Key: ${toField.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${toField.sampleData?.slice(0, 3).join(', ') || 'None'}

Please return explanation in JSON format:
{
  "summary": "Brief one-sentence explanation of this relationship",
  "detailedExplanation": "Detailed explanation of why these fields are related, from both technical and business perspectives",
  "businessContext": "Business significance and purpose of this relationship",
  "technicalDetails": "Technical implementation details and considerations",
  "confidence": "Plain language explanation of the confidence level and reasoning",
  "recommendations": ["Specific recommendations for users"],
  "language": "en"
}

Requirements:
1. Use clear, understandable language suitable for ${levelDescription}
2. Avoid excessive technical jargon
3. Focus on business value and practical applications
4. If confidence is low, explain reasons and risks
`;
    }
  }

  /**
   * 构建简要解释提示词
   */
  private buildBriefExplanationPrompt(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    maxLength: number
  ): string {
    const language = this.config.language;

    if (language === 'zh') {
      return `
请用一句话（不超过${maxLength}字）解释以下关联关系：

${fromField.tableName}.${fromField.name} → ${toField.tableName}.${toField.name}
关联类型: ${relationship.relationshipType}
置信度: ${relationship.confidenceScore}

要求：简洁明了，突出关键信息。
`;
    } else {
      return `
Please explain the following relationship in one sentence (max ${maxLength} characters):

${fromField.tableName}.${fromField.name} → ${toField.tableName}.${toField.name}
Type: ${relationship.relationshipType}
Confidence: ${relationship.confidenceScore}

Requirements: Concise and clear, highlighting key information.
`;
    }
  }

  /**
   * 构建批量解释提示词
   */
  private buildBatchExplanationPrompt(
    relationships: Array<{
      relationship: RelationshipDiscoveryResult;
      fromField: FieldInfo;
      toField: FieldInfo;
    }>,
    userLevel: string
  ): string {
    const language = this.config.language;
    const levelDescription = this.getUserLevelDescription(userLevel, language);

    const relationshipsDescription = relationships.map((item, index) => {
      return `
关联关系 ${index + 1}:
- 源字段: ${item.fromField.tableName}.${item.fromField.name}
- 目标字段: ${item.toField.tableName}.${item.toField.name}
- 关联类型: ${item.relationship.relationshipType}
- 置信度: ${item.relationship.confidenceScore}
`;
    }).join('\n');

    if (language === 'zh') {
      return `
请为以下关联关系批量生成${levelDescription}的解释：

${relationshipsDescription}

请为每个关联关系返回解释，格式如下：
{
  "results": [
    {
      "relationshipIndex": 0,
      "summary": "简要说明",
      "detailedExplanation": "详细解释",
      "businessContext": "业务上下文",
      "technicalDetails": "技术细节",
      "confidence": "置信度说明",
      "recommendations": ["建议列表"],
      "language": "zh"
    }
    // ... 其他关联关系的解释
  ]
}
`;
    } else {
      return `
Please generate explanations for the following relationships in batch, suitable for ${levelDescription}:

${relationshipsDescription}

Please return explanations for each relationship in the following format:
{
  "results": [
    {
      "relationshipIndex": 0,
      "summary": "Brief explanation",
      "detailedExplanation": "Detailed explanation",
      "businessContext": "Business context",
      "technicalDetails": "Technical details",
      "confidence": "Confidence explanation",
      "recommendations": ["Recommendation list"],
      "language": "en"
    }
    // ... explanations for other relationships
  ]
}
`;
    }
  }

  /**
   * 构建个性化解释提示词
   */
  private buildPersonalizedExplanationPrompt(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    userProfile: any
  ): string {
    const language = userProfile.language;

    if (language === 'zh') {
      return `
请为以下用户生成个性化的关联关系解释：

用户画像：
- 角色: ${userProfile.role}
- 经验水平: ${userProfile.experience}
- 领域: ${userProfile.domain}
- 偏好: ${userProfile.preferences.join(', ')}

关联关系：
${fromField.tableName}.${fromField.name} → ${toField.tableName}.${toField.name}
类型: ${relationship.relationshipType}
置信度: ${relationship.confidenceScore}

请根据用户画像调整解释的深度、术语使用和关注点。
`;
    } else {
      return `
Please generate a personalized explanation for the following user:

User Profile:
- Role: ${userProfile.role}
- Experience: ${userProfile.experience}
- Domain: ${userProfile.domain}
- Preferences: ${userProfile.preferences.join(', ')}

Relationship:
${fromField.tableName}.${fromField.name} → ${toField.tableName}.${toField.name}
Type: ${relationship.relationshipType}
Confidence: ${relationship.confidenceScore}

Please adjust the explanation depth, terminology, and focus based on the user profile.
`;
    }
  }

  /**
   * 解析解释响应
   */
  private parseExplanationResponse(content: string): RelationshipExplanation {
    try {
      const parsed = JSON.parse(content);
      return {
        summary: parsed.summary || '无法生成摘要',
        detailedExplanation: parsed.detailedExplanation || '无法生成详细解释',
        businessContext: parsed.businessContext || '无业务上下文',
        technicalDetails: parsed.technicalDetails || '无技术细节',
        confidence: parsed.confidence || '置信度未知',
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [],
        language: parsed.language || this.config.language
      };
    } catch (error) {
      console.error('解析解释响应失败:', error);
      throw new Error('Invalid explanation response format');
    }
  }

  /**
   * 解析批量解释响应
   */
  private parseBatchExplanationResponse(
    content: string,
    relationships: Array<any>
  ): RelationshipExplanation[] {
    try {
      const parsed = JSON.parse(content);
      const results = parsed.results || [];
      
      return relationships.map((item, index) => {
        const result = results.find((r: any) => r.relationshipIndex === index);
        if (result) {
          return this.parseExplanationResponse(JSON.stringify(result));
        } else {
          return this.getDefaultExplanation(item.relationship, item.fromField, item.toField, 'general');
        }
      });
    } catch (error) {
      console.error('解析批量解释响应失败:', error);
      return relationships.map(item => 
        this.getDefaultExplanation(item.relationship, item.fromField, item.toField, 'general')
      );
    }
  }

  /**
   * 获取默认解释
   */
  private getDefaultExplanation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    userLevel: string
  ): RelationshipExplanation {
    const language = this.config.language;
    
    if (language === 'zh') {
      return {
        summary: `${fromField.name}与${toField.name}存在${relationship.relationshipType}关联关系`,
        detailedExplanation: `系统通过${relationship.discoveryMethod}方法发现这两个字段可能存在关联关系，置信度为${relationship.confidenceScore}。`,
        businessContext: '该关联关系可能对数据分析和业务理解有帮助。',
        technicalDetails: `关联类型为${relationship.relationshipType}，建议进一步验证。`,
        confidence: `置信度为${relationship.confidence}，建议谨慎使用。`,
        recommendations: ['建议人工验证该关联关系', '考虑收集更多证据'],
        language: 'zh'
      };
    } else {
      return {
        summary: `${fromField.name} has a ${relationship.relationshipType} relationship with ${toField.name}`,
        detailedExplanation: `The system discovered this potential relationship using ${relationship.discoveryMethod} method with confidence ${relationship.confidenceScore}.`,
        businessContext: 'This relationship may be helpful for data analysis and business understanding.',
        technicalDetails: `Relationship type is ${relationship.relationshipType}, further validation recommended.`,
        confidence: `Confidence level is ${relationship.confidence}, use with caution.`,
        recommendations: ['Manual verification recommended', 'Consider gathering more evidence'],
        language: 'en'
      };
    }
  }

  /**
   * 获取默认简要解释
   */
  private getDefaultBriefExplanation(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo
  ): string {
    const language = this.config.language;
    
    if (language === 'zh') {
      return `${fromField.name}与${toField.name}可能存在${relationship.relationshipType}关联`;
    } else {
      return `${fromField.name} may have ${relationship.relationshipType} relationship with ${toField.name}`;
    }
  }

  /**
   * 获取用户级别描述
   */
  private getUserLevelDescription(userLevel: string, language: 'zh' | 'en'): string {
    const descriptions = {
      zh: {
        technical: '技术人员',
        business: '业务人员',
        general: '普通用户'
      },
      en: {
        technical: 'technical users',
        business: 'business users',
        general: 'general users'
      }
    };
    
    return descriptions[language][userLevel] || descriptions[language].general;
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(
    relationship: RelationshipDiscoveryResult,
    fromField: FieldInfo,
    toField: FieldInfo,
    userLevel: string
  ): string {
    return `explanation_${fromField.tableName}.${fromField.name}_${toField.tableName}.${toField.name}_${userLevel}_${this.config.language}`;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.explanationCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number } {
    return {
      size: this.explanationCache.size
    };
  }
}
