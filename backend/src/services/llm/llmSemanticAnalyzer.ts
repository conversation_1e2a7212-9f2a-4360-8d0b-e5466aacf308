import {
  FieldInfo,
  SemanticAnalysis,
  LLMEnhancementConfig,
  RelationshipType,
  ConfidenceLevel
} from '../../types';
import { LLMClient } from './llmClient';

/**
 * LLM语义分析器
 * 使用大模型理解字段名称、描述的语义含义，提升关联关系判断的准确性
 */
export class LLMSemanticAnalyzer {
  private llmClient: LLMClient;
  private config: LLMEnhancementConfig;
  private cache: Map<string, SemanticAnalysis> = new Map();

  constructor(config: LLMEnhancementConfig) {
    this.config = config;
    this.llmClient = new LLMClient(config);
  }

  /**
   * 分析两个字段的语义相似性
   */
  async analyzeSemanticSimilarity(
    field1: FieldInfo,
    field2: FieldInfo
  ): Promise<SemanticAnalysis> {
    const cacheKey = this.generateCacheKey(field1, field2);
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const prompt = this.buildSemanticAnalysisPrompt(field1, field2);
      const response = await this.llmClient.chat({
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens,
        response_format: { type: 'json_object' }
      });

      const analysis = this.parseSemanticResponse(response.content);
      
      // 缓存结果
      if (this.config.enableCache) {
        this.cache.set(cacheKey, analysis);
      }

      return analysis;
    } catch (error) {
      console.error('LLM语义分析失败:', error);
      // 返回默认分析结果
      return this.getDefaultSemanticAnalysis(field1, field2);
    }
  }

  /**
   * 批量分析多个字段对的语义相似性
   */
  async analyzeBatchSemanticSimilarity(
    fieldPairs: Array<{ field1: FieldInfo; field2: FieldInfo }>
  ): Promise<SemanticAnalysis[]> {
    const batchSize = this.config.batchSize;
    const results: SemanticAnalysis[] = [];

    for (let i = 0; i < fieldPairs.length; i += batchSize) {
      const batch = fieldPairs.slice(i, i + batchSize);
      const batchPrompt = this.buildBatchSemanticAnalysisPrompt(batch);

      try {
        const response = await this.llmClient.chat({
          messages: [{ role: 'user', content: batchPrompt }],
          temperature: this.config.temperature,
          max_tokens: this.config.maxTokens * batch.length,
          response_format: { type: 'json_object' }
        });

        const batchResults = this.parseBatchSemanticResponse(response.content, batch);
        results.push(...batchResults);
      } catch (error) {
        console.error('批量LLM语义分析失败:', error);
        // 为失败的批次返回默认结果
        const defaultResults = batch.map(pair => 
          this.getDefaultSemanticAnalysis(pair.field1, pair.field2)
        );
        results.push(...defaultResults);
      }
    }

    return results;
  }

  /**
   * 构建语义分析提示词
   */
  private buildSemanticAnalysisPrompt(field1: FieldInfo, field2: FieldInfo): string {
    const language = this.config.language;
    
    if (language === 'zh') {
      return `
作为数据关系分析专家，请分析以下两个字段是否可能存在关联关系：

字段1信息：
- 名称: ${field1.name}
- 描述: ${field1.description || '无'}
- 数据类型: ${field1.dataType}
- 所属表: ${field1.tableName}
- 是否主键: ${field1.isPrimaryKey ? '是' : '否'}
- 样本数据: ${field1.sampleData ? field1.sampleData.slice(0, 5).join(', ') : '无'}

字段2信息：
- 名称: ${field2.name}
- 描述: ${field2.description || '无'}
- 数据类型: ${field2.dataType}
- 所属表: ${field2.tableName}
- 是否主键: ${field2.isPrimaryKey ? '是' : '否'}
- 样本数据: ${field2.sampleData ? field2.sampleData.slice(0, 5).join(', ') : '无'}

请从以下维度分析并以JSON格式返回结果：
{
  "semanticSimilarity": 0.0-1.0, // 语义相似性分数
  "businessRelevance": 0.0-1.0,  // 业务关联性分数
  "dataTypeCompatibility": 0.0-1.0, // 数据类型兼容性分数
  "suggestedRelationType": "one_to_one|one_to_many|many_to_one", // 建议的关联类型
  "explanation": "详细解释为什么认为这两个字段相关或不相关，包括语义分析、业务逻辑等",
  "confidence": "high|medium|low" // 整体置信度等级
}

分析要点：
1. 考虑字段名称的语义含义，包括同义词、缩写、多语言混合
2. 分析业务逻辑上的关联可能性
3. 评估数据类型的兼容性
4. 基于样本数据判断关联的合理性
5. 考虑主键-外键的典型模式
`;
    } else {
      return `
As a data relationship analysis expert, please analyze whether the following two fields might have a relationship:

Field 1 Information:
- Name: ${field1.name}
- Description: ${field1.description || 'None'}
- Data Type: ${field1.dataType}
- Table: ${field1.tableName}
- Is Primary Key: ${field1.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${field1.sampleData ? field1.sampleData.slice(0, 5).join(', ') : 'None'}

Field 2 Information:
- Name: ${field2.name}
- Description: ${field2.description || 'None'}
- Data Type: ${field2.dataType}
- Table: ${field2.tableName}
- Is Primary Key: ${field2.isPrimaryKey ? 'Yes' : 'No'}
- Sample Data: ${field2.sampleData ? field2.sampleData.slice(0, 5).join(', ') : 'None'}

Please analyze from the following dimensions and return results in JSON format:
{
  "semanticSimilarity": 0.0-1.0, // Semantic similarity score
  "businessRelevance": 0.0-1.0,  // Business relevance score
  "dataTypeCompatibility": 0.0-1.0, // Data type compatibility score
  "suggestedRelationType": "one_to_one|one_to_many|many_to_one", // Suggested relationship type
  "explanation": "Detailed explanation of why these fields are related or not, including semantic analysis and business logic",
  "confidence": "high|medium|low" // Overall confidence level
}

Analysis points:
1. Consider semantic meaning of field names, including synonyms, abbreviations, multilingual mixing
2. Analyze business logic relationship possibilities
3. Evaluate data type compatibility
4. Judge relationship reasonableness based on sample data
5. Consider typical primary key-foreign key patterns
`;
    }
  }

  /**
   * 构建批量语义分析提示词
   */
  private buildBatchSemanticAnalysisPrompt(
    fieldPairs: Array<{ field1: FieldInfo; field2: FieldInfo }>
  ): string {
    const language = this.config.language;
    const pairsDescription = fieldPairs.map((pair, index) => {
      return `
字段对 ${index + 1}:
字段A: ${pair.field1.name} (${pair.field1.tableName}.${pair.field1.name}, ${pair.field1.dataType})
字段B: ${pair.field2.name} (${pair.field2.tableName}.${pair.field2.name}, ${pair.field2.dataType})
`;
    }).join('\n');

    if (language === 'zh') {
      return `
请批量分析以下字段对的语义相似性：

${pairsDescription}

请为每个字段对返回分析结果，格式如下：
{
  "results": [
    {
      "pairIndex": 0,
      "semanticSimilarity": 0.0-1.0,
      "businessRelevance": 0.0-1.0,
      "dataTypeCompatibility": 0.0-1.0,
      "suggestedRelationType": "one_to_one|one_to_many|many_to_one",
      "explanation": "分析说明",
      "confidence": "high|medium|low"
    }
    // ... 其他字段对的结果
  ]
}
`;
    } else {
      return `
Please analyze the semantic similarity of the following field pairs in batch:

${pairsDescription}

Please return analysis results for each field pair in the following format:
{
  "results": [
    {
      "pairIndex": 0,
      "semanticSimilarity": 0.0-1.0,
      "businessRelevance": 0.0-1.0,
      "dataTypeCompatibility": 0.0-1.0,
      "suggestedRelationType": "one_to_one|one_to_many|many_to_one",
      "explanation": "Analysis explanation",
      "confidence": "high|medium|low"
    }
    // ... results for other field pairs
  ]
}
`;
    }
  }

  /**
   * 解析语义分析响应
   */
  private parseSemanticResponse(content: string): SemanticAnalysis {
    try {
      const parsed = JSON.parse(content);
      return {
        semanticSimilarity: Math.max(0, Math.min(1, parsed.semanticSimilarity || 0)),
        businessRelevance: Math.max(0, Math.min(1, parsed.businessRelevance || 0)),
        dataTypeCompatibility: Math.max(0, Math.min(1, parsed.dataTypeCompatibility || 0)),
        suggestedRelationType: parsed.suggestedRelationType || 'many_to_one',
        explanation: parsed.explanation || '无法生成解释',
        confidence: parsed.confidence || 'low'
      };
    } catch (error) {
      console.error('解析语义分析响应失败:', error);
      throw new Error('Invalid LLM response format');
    }
  }

  /**
   * 解析批量语义分析响应
   */
  private parseBatchSemanticResponse(
    content: string,
    fieldPairs: Array<{ field1: FieldInfo; field2: FieldInfo }>
  ): SemanticAnalysis[] {
    try {
      const parsed = JSON.parse(content);
      const results = parsed.results || [];
      
      return fieldPairs.map((pair, index) => {
        const result = results.find((r: any) => r.pairIndex === index);
        if (result) {
          return this.parseSemanticResponse(JSON.stringify(result));
        } else {
          return this.getDefaultSemanticAnalysis(pair.field1, pair.field2);
        }
      });
    } catch (error) {
      console.error('解析批量语义分析响应失败:', error);
      return fieldPairs.map(pair => this.getDefaultSemanticAnalysis(pair.field1, pair.field2));
    }
  }

  /**
   * 获取默认语义分析结果
   */
  private getDefaultSemanticAnalysis(field1: FieldInfo, field2: FieldInfo): SemanticAnalysis {
    return {
      semanticSimilarity: 0.1,
      businessRelevance: 0.1,
      dataTypeCompatibility: field1.dataType === field2.dataType ? 0.8 : 0.2,
      suggestedRelationType: 'many_to_one',
      explanation: 'LLM分析失败，使用默认评估',
      confidence: 'low'
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(field1: FieldInfo, field2: FieldInfo): string {
    const key1 = `${field1.tableName}.${field1.name}.${field1.dataType}`;
    const key2 = `${field2.tableName}.${field2.name}.${field2.dataType}`;
    return `semantic_${key1}_${key2}`;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // 需要实现命中率统计
    };
  }
}
