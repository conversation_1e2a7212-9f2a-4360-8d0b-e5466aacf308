import { PrismaClient } from '@prisma/client';
import { RelationshipType, AppError, DatabaseError } from '../types';

export class SemanticService {
  constructor(private prisma: PrismaClient) {}

  /**
   * 更新数据表别名和描述
   */
  async updateTableAlias(tableId: string, aliasName?: string, description?: string): Promise<void> {
    try {
      const table = await this.prisma.dataTable.findUnique({
        where: { id: tableId }
      });

      if (!table) {
        throw new AppError('数据表不存在', 404);
      }

      await this.prisma.dataTable.update({
        where: { id: tableId },
        data: {
          aliasName: aliasName || null,
          description: description || null
        }
      });

      // 清除语义层缓存
      await this.clearSemanticCache();
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('更新表别名失败');
    }
  }

  /**
   * 更新数据列别名和描述
   */
  async updateColumnAlias(columnId: string, aliasName?: string, description?: string): Promise<void> {
    try {
      const column = await this.prisma.dataColumn.findUnique({
        where: { id: columnId }
      });

      if (!column) {
        throw new AppError('数据列不存在', 404);
      }

      await this.prisma.dataColumn.update({
        where: { id: columnId },
        data: {
          aliasName: aliasName || null,
          description: description || null
        }
      });

      // 清除语义层缓存
      await this.clearSemanticCache();
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('更新列别名失败');
    }
  }

  /**
   * 创建数据关联关系
   */
  async createRelationship(
    fromColumnId: string, 
    toColumnId: string, 
    relationshipType: RelationshipType
  ): Promise<string> {
    try {
      // 验证列是否存在
      const [fromColumn, toColumn] = await Promise.all([
        this.prisma.dataColumn.findUnique({ where: { id: fromColumnId } }),
        this.prisma.dataColumn.findUnique({ where: { id: toColumnId } })
      ]);

      if (!fromColumn) {
        throw new AppError('起始列不存在', 404);
      }

      if (!toColumn) {
        throw new AppError('目标列不存在', 404);
      }

      // 检查是否已存在相同的关联关系
      const existingRelationship = await this.prisma.dataRelationship.findFirst({
        where: {
          OR: [
            { fromColumnId, toColumnId },
            { fromColumnId: toColumnId, toColumnId: fromColumnId }
          ]
        }
      });

      if (existingRelationship) {
        throw new AppError('该关联关系已存在', 400);
      }

      // 创建关联关系
      const relationship = await this.prisma.dataRelationship.create({
        data: {
          fromColumnId,
          toColumnId,
          relationshipType,
          isManual: true
        }
      });

      // 清除语义层缓存
      await this.clearSemanticCache();

      return relationship.id;
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('创建关联关系失败');
    }
  }

  /**
   * 更新关联关系
   */
  async updateRelationship(relationshipId: string, relationshipType: RelationshipType): Promise<void> {
    try {
      const relationship = await this.prisma.dataRelationship.findUnique({
        where: { id: relationshipId }
      });

      if (!relationship) {
        throw new AppError('关联关系不存在', 404);
      }

      await this.prisma.dataRelationship.update({
        where: { id: relationshipId },
        data: { relationshipType }
      });

      // 清除语义层缓存
      await this.clearSemanticCache();
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('更新关联关系失败');
    }
  }

  /**
   * 删除关联关系
   */
  async deleteRelationship(relationshipId: string): Promise<void> {
    try {
      const relationship = await this.prisma.dataRelationship.findUnique({
        where: { id: relationshipId }
      });

      if (!relationship) {
        throw new AppError('关联关系不存在', 404);
      }

      await this.prisma.dataRelationship.delete({
        where: { id: relationshipId }
      });

      // 清除语义层缓存
      await this.clearSemanticCache();
    } catch (error) {
      if (error instanceof AppError) throw error;
      throw new DatabaseError('删除关联关系失败');
    }
  }

  /**
   * 获取语义层上下文（用于LLM）
   */
  async getSemanticContext(): Promise<string> {
    try {
      // 尝试从缓存获取
      const cachedContext = await this.getSemanticContextFromCache();
      if (cachedContext) {
        return cachedContext;
      }

      // 构建语义层上下文
      const context = await this.buildSemanticContext();
      
      // 缓存上下文
      await this.cacheSemanticContext(context);
      
      return context;
    } catch (error) {
      throw new DatabaseError('获取语义层上下文失败');
    }
  }

  /**
   * 自动发现关联关系
   */
  async discoverRelationships(dataSourceId: string): Promise<number> {
    try {
      // 获取数据源的所有表和列
      const dataTables = await this.prisma.dataTable.findMany({
        where: { dataSourceId },
        include: {
          dataColumns: true
        }
      });

      let discoveredCount = 0;

      // 简单的关联关系发现逻辑
      // 查找名称相似的列（如 id 和 customer_id）
      for (const table of dataTables) {
        for (const column of table.dataColumns) {
          if (column.isPrimaryKey) {
            // 查找其他表中可能关联到这个主键的外键
            const potentialForeignKeys = await this.findPotentialForeignKeys(
              column.originalColumnName,
              table.id,
              dataTables
            );

            for (const fk of potentialForeignKeys) {
              // 检查是否已存在关联关系
              const existingRelationship = await this.prisma.dataRelationship.findFirst({
                where: {
                  OR: [
                    { fromColumnId: column.id, toColumnId: fk.id },
                    { fromColumnId: fk.id, toColumnId: column.id }
                  ]
                }
              });

              if (!existingRelationship) {
                await this.prisma.dataRelationship.create({
                  data: {
                    fromColumnId: column.id,
                    toColumnId: fk.id,
                    relationshipType: 'one_to_many',
                    isManual: false
                  }
                });
                discoveredCount++;
              }
            }
          }
        }
      }

      // 清除语义层缓存
      await this.clearSemanticCache();

      return discoveredCount;
    } catch (error) {
      throw new DatabaseError('自动发现关联关系失败');
    }
  }

  // 私有方法
  private async buildSemanticContext(): Promise<string> {
    const dataSources = await this.prisma.dataSource.findMany({
      include: {
        dataTables: {
          include: {
            dataColumns: true
          }
        }
      }
    });

    const relationships = await this.prisma.dataRelationship.findMany({
      include: {
        fromColumn: {
          include: {
            dataTable: true
          }
        },
        toColumn: {
          include: {
            dataTable: true
          }
        }
      }
    });

    let context = '# 数据模型语义层\n\n';

    // 构建数据源和表的描述
    for (const dataSource of dataSources) {
      context += `## 数据源: ${dataSource.name}\n`;
      
      for (const table of dataSource.dataTables) {
        const tableName = table.aliasName || table.originalTableName;
        context += `### 表: ${tableName}\n`;
        if (table.description) {
          context += `描述: ${table.description}\n`;
        }
        
        context += '字段:\n';
        for (const column of table.dataColumns) {
          const columnName = column.aliasName || column.originalColumnName;
          context += `- ${columnName} (${column.originalDataType})`;
          if (column.isPrimaryKey) context += ' [主键]';
          if (column.description) context += ` - ${column.description}`;
          context += '\n';
        }
        context += '\n';
      }
    }

    // 构建关联关系描述
    if (relationships.length > 0) {
      context += '## 表关联关系\n';
      for (const rel of relationships) {
        const fromTable = rel.fromColumn.dataTable.aliasName || rel.fromColumn.dataTable.originalTableName;
        const fromColumn = rel.fromColumn.aliasName || rel.fromColumn.originalColumnName;
        const toTable = rel.toColumn.dataTable.aliasName || rel.toColumn.dataTable.originalTableName;
        const toColumn = rel.toColumn.aliasName || rel.toColumn.originalColumnName;
        
        context += `- ${fromTable}.${fromColumn} -> ${toTable}.${toColumn} (${rel.relationshipType})\n`;
      }
    }

    return context;
  }

  private async findPotentialForeignKeys(primaryKeyName: string, excludeTableId: string, allTables: any[]): Promise<any[]> {
    const potentialForeignKeys = [];
    
    // 简单的命名约定匹配
    const patterns = [
      `${primaryKeyName}`, // 完全匹配
      `${primaryKeyName.replace('_id', '')}_id`, // 如果主键是 id，查找 table_id
      `${primaryKeyName.replace('id', '')}_id` // 其他变体
    ];

    for (const table of allTables) {
      if (table.id === excludeTableId) continue;
      
      for (const column of table.dataColumns) {
        if (patterns.some(pattern => column.originalColumnName.toLowerCase().includes(pattern.toLowerCase()))) {
          potentialForeignKeys.push(column);
        }
      }
    }

    return potentialForeignKeys;
  }

  private async getSemanticContextFromCache(): Promise<string | null> {
    // 这里应该从Redis缓存获取
    // 暂时返回null，表示没有缓存
    return null;
  }

  private async cacheSemanticContext(context: string): Promise<void> {
    // 这里应该将上下文缓存到Redis
    // 暂时不实现
  }

  private async clearSemanticCache(): Promise<void> {
    // 这里应该清除Redis中的语义层缓存
    // 暂时不实现
  }
}
