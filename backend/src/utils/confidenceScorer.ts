import {
  RelationshipDiscoveryMethod,
  ConfidenceLevel,
  RelationshipDiscoveryResult
} from '../types';

/**
 * 置信度评分器
 * 为关联关系发现结果提供统一的置信度评分机制
 */
export class ConfidenceScorer {
  // 不同发现方法的基础权重
  private static readonly METHOD_WEIGHTS: Record<RelationshipDiscoveryMethod, number> = {
    foreign_key: 0.95,      // 外键约束最可靠
    naming_convention: 0.7,  // 命名约定较可靠
    data_analysis: 0.6,     // 数据分析中等可靠
    manual: 1.0            // 手动创建最可靠
  };

  // 置信度等级阈值
  private static readonly CONFIDENCE_THRESHOLDS = {
    high: 0.8,
    medium: 0.5,
    low: 0.0
  };

  /**
   * 计算综合置信度分数
   */
  static calculateConfidenceScore(
    method: RelationshipDiscoveryMethod,
    evidenceFactors: EvidenceFactor[]
  ): number {
    const baseWeight = this.METHOD_WEIGHTS[method];
    
    // 计算证据因子的加权平均
    let evidenceScore = 0;
    let totalWeight = 0;

    for (const factor of evidenceFactors) {
      evidenceScore += factor.score * factor.weight;
      totalWeight += factor.weight;
    }

    if (totalWeight > 0) {
      evidenceScore /= totalWeight;
    }

    // 结合基础权重和证据分数
    const finalScore = baseWeight * 0.6 + evidenceScore * 0.4;
    
    // 确保分数在0-1范围内
    return Math.max(0, Math.min(1, finalScore));
  }

  /**
   * 将分数转换为置信度等级
   */
  static scoreToConfidenceLevel(score: number): ConfidenceLevel {
    if (score >= this.CONFIDENCE_THRESHOLDS.high) return 'high';
    if (score >= this.CONFIDENCE_THRESHOLDS.medium) return 'medium';
    return 'low';
  }

  /**
   * 为外键约束关系计算置信度
   */
  static calculateForeignKeyConfidence(
    hasConstraint: boolean,
    dataTypeMatch: boolean,
    namingMatch: boolean
  ): { score: number; evidence: string[] } {
    const evidence: string[] = [];
    const factors: EvidenceFactor[] = [];

    if (hasConstraint) {
      factors.push({ score: 1.0, weight: 0.8, description: '数据库外键约束' });
      evidence.push('数据库外键约束');
    }

    if (dataTypeMatch) {
      factors.push({ score: 0.8, weight: 0.3, description: '数据类型匹配' });
      evidence.push('数据类型匹配');
    }

    if (namingMatch) {
      factors.push({ score: 0.7, weight: 0.2, description: '命名约定匹配' });
      evidence.push('命名约定匹配');
    }

    const score = this.calculateConfidenceScore('foreign_key', factors);
    return { score, evidence };
  }

  /**
   * 为命名约定关系计算置信度
   */
  static calculateNamingConventionConfidence(
    exactMatch: boolean,
    patternMatch: boolean,
    fuzzyMatch: boolean,
    fuzzyScore: number,
    dataTypeMatch: boolean
  ): { score: number; evidence: string[] } {
    const evidence: string[] = [];
    const factors: EvidenceFactor[] = [];

    if (exactMatch) {
      factors.push({ score: 0.9, weight: 0.8, description: '完全匹配' });
      evidence.push('列名完全匹配');
    } else if (patternMatch) {
      factors.push({ score: 0.8, weight: 0.6, description: '模式匹配' });
      evidence.push('符合命名模式');
    } else if (fuzzyMatch) {
      factors.push({ 
        score: fuzzyScore, 
        weight: 0.4, 
        description: `模糊匹配 (${(fuzzyScore * 100).toFixed(1)}%)` 
      });
      evidence.push(`模糊匹配 (相似度: ${(fuzzyScore * 100).toFixed(1)}%)`);
    }

    if (dataTypeMatch) {
      factors.push({ score: 0.7, weight: 0.3, description: '数据类型匹配' });
      evidence.push('数据类型兼容');
    }

    const score = this.calculateConfidenceScore('naming_convention', factors);
    return { score, evidence };
  }

  /**
   * 为数据分析关系计算置信度
   */
  static calculateDataAnalysisConfidence(
    valueOverlap: number,
    uniquenessRatio: number,
    dataTypeMatch: boolean,
    sampleSize: number
  ): { score: number; evidence: string[] } {
    const evidence: string[] = [];
    const factors: EvidenceFactor[] = [];

    // 值重叠分析
    if (valueOverlap > 0.3) {
      factors.push({ 
        score: Math.min(0.9, valueOverlap + 0.1), 
        weight: 0.5, 
        description: `值重叠率: ${(valueOverlap * 100).toFixed(1)}%` 
      });
      evidence.push(`值重叠率: ${(valueOverlap * 100).toFixed(1)}%`);
    }

    // 唯一性分析
    if (uniquenessRatio > 0.8) {
      factors.push({ 
        score: 0.8, 
        weight: 0.3, 
        description: `高唯一性: ${(uniquenessRatio * 100).toFixed(1)}%` 
      });
      evidence.push(`高唯一性 (${(uniquenessRatio * 100).toFixed(1)}%)`);
    }

    // 数据类型匹配
    if (dataTypeMatch) {
      factors.push({ score: 0.6, weight: 0.2, description: '数据类型匹配' });
      evidence.push('数据类型匹配');
    }

    // 样本大小调整
    const sampleSizeAdjustment = Math.min(1.0, sampleSize / 100);
    factors.forEach(factor => {
      factor.score *= sampleSizeAdjustment;
    });

    if (sampleSize < 50) {
      evidence.push(`样本量较小 (${sampleSize})`);
    }

    const score = this.calculateConfidenceScore('data_analysis', factors);
    return { score, evidence };
  }

  /**
   * 为跨数据源关系计算置信度
   */
  static calculateCrossSourceConfidence(
    namingScore: number,
    dataOverlap: number,
    typeMatch: boolean,
    sourceTypes: string[]
  ): { score: number; evidence: string[] } {
    const evidence: string[] = [];
    const factors: EvidenceFactor[] = [];

    // 命名匹配
    if (namingScore > 0.5) {
      factors.push({ 
        score: namingScore, 
        weight: 0.4, 
        description: `命名匹配: ${(namingScore * 100).toFixed(1)}%` 
      });
      evidence.push(`命名匹配 (${(namingScore * 100).toFixed(1)}%)`);
    }

    // 数据重叠
    if (dataOverlap > 0.3) {
      factors.push({ 
        score: dataOverlap, 
        weight: 0.5, 
        description: `数据重叠: ${(dataOverlap * 100).toFixed(1)}%` 
      });
      evidence.push(`跨源数据重叠 (${(dataOverlap * 100).toFixed(1)}%)`);
    }

    // 类型匹配
    if (typeMatch) {
      factors.push({ score: 0.6, weight: 0.2, description: '数据类型匹配' });
      evidence.push('数据类型兼容');
    }

    // 跨数据源关系的置信度通常较低
    const crossSourcePenalty = 0.8;
    const score = this.calculateConfidenceScore('naming_convention', factors) * crossSourcePenalty;
    
    evidence.push(`跨数据源关系 (${sourceTypes.join(' -> ')})`);

    return { score, evidence };
  }

  /**
   * 批量评估关联关系置信度
   */
  static evaluateRelationships(
    relationships: RelationshipDiscoveryResult[]
  ): RelationshipDiscoveryResult[] {
    return relationships.map(relationship => {
      // 如果已有置信度分数，直接使用
      if (relationship.confidenceScore !== undefined) {
        return {
          ...relationship,
          confidence: this.scoreToConfidenceLevel(relationship.confidenceScore)
        };
      }

      // 否则根据方法和证据重新计算
      const factors: EvidenceFactor[] = relationship.evidence?.map(evidence => ({
        score: 0.7, // 默认分数
        weight: 1.0,
        description: evidence
      })) || [];

      const score = this.calculateConfidenceScore(relationship.discoveryMethod, factors);

      return {
        ...relationship,
        confidenceScore: score,
        confidence: this.scoreToConfidenceLevel(score)
      };
    });
  }

  /**
   * 获取置信度统计信息
   */
  static getConfidenceStatistics(
    relationships: RelationshipDiscoveryResult[]
  ): {
    total: number;
    high: number;
    medium: number;
    low: number;
    averageScore: number;
    byMethod: Record<RelationshipDiscoveryMethod, number>;
  } {
    const stats = {
      total: relationships.length,
      high: 0,
      medium: 0,
      low: 0,
      averageScore: 0,
      byMethod: {} as Record<RelationshipDiscoveryMethod, number>
    };

    let totalScore = 0;

    for (const relationship of relationships) {
      // 统计置信度等级
      switch (relationship.confidence) {
        case 'high':
          stats.high++;
          break;
        case 'medium':
          stats.medium++;
          break;
        case 'low':
          stats.low++;
          break;
      }

      // 累计分数
      if (relationship.confidenceScore !== undefined) {
        totalScore += relationship.confidenceScore;
      }

      // 按方法统计
      const method = relationship.discoveryMethod;
      stats.byMethod[method] = (stats.byMethod[method] || 0) + 1;
    }

    stats.averageScore = stats.total > 0 ? totalScore / stats.total : 0;

    return stats;
  }
}

/**
 * 证据因子接口
 */
export interface EvidenceFactor {
  score: number;      // 0-1之间的分数
  weight: number;     // 权重
  description: string; // 描述
}
