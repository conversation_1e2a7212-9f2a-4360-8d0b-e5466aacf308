import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const SECRET_KEY = process.env.ENCRYPTION_KEY || 'your-32-character-secret-key-here!!';

// 确保密钥长度为32字节
const key = crypto.scryptSync(SECRET_KEY, 'salt', 32);

/**
 * 加密敏感数据
 */
export function encrypt(text: string): string {
  try {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(ALGORITHM, key);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    // 返回格式: iv:authTag:encryptedData
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  } catch (error) {
    throw new Error('加密失败');
  }
}

/**
 * 解密敏感数据
 */
export function decrypt(encryptedData: string): string {
  try {
    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      throw new Error('无效的加密数据格式');
    }
    
    const [ivHex, authTagHex, encrypted] = parts;
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(ALGORITHM, key);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    throw new Error('解密失败');
  }
}

/**
 * 加密连接配置
 */
export function encryptConnectionConfig(config: any): string {
  return encrypt(JSON.stringify(config));
}

/**
 * 解密连接配置
 */
export function decryptConnectionConfig(encryptedConfig: string): any {
  const decryptedString = decrypt(encryptedConfig);
  return JSON.parse(decryptedString);
}
