/**
 * 增强的命名约定匹配器
 * 支持多种命名模式和智能匹配算法
 */
export class NamingConventionMatcher {
  private static readonly DEFAULT_PATTERNS = [
    // 完全匹配
    '{column}',
    
    // 主键-外键模式
    'id -> {table}_id',
    '{table}_id -> id',
    '{column}_id',
    
    // 表名前缀模式
    '{table}_{column}',
    '{table}.{column}',
    
    // 常见缩写模式
    '{column}_ref',
    '{column}_key',
    'ref_{column}',
    'key_{column}',
    
    // 复数/单数转换
    '{column}s -> {column}',
    '{column} -> {column}s',
    
    // 下划线/驼峰转换
    '{snake_case} -> {camelCase}',
    '{camelCase} -> {snake_case}'
  ];

  private patterns: string[];
  private tableNames: Set<string> = new Set();

  constructor(customPatterns?: string[]) {
    this.patterns = customPatterns || NamingConventionMatcher.DEFAULT_PATTERNS;
  }

  /**
   * 设置表名列表，用于模式匹配
   */
  setTableNames(tableNames: string[]): void {
    this.tableNames = new Set(tableNames.map(name => name.toLowerCase()));
  }

  /**
   * 检查两个列名是否匹配命名约定
   */
  matches(column1: string, column2: string, table1?: string, table2?: string): boolean {
    const result = this.calculateMatchScore(column1, column2, table1, table2);
    return result.score > 0.5;
  }

  /**
   * 计算两个列名的匹配分数和详细信息
   */
  calculateMatchScore(
    column1: string, 
    column2: string, 
    table1?: string, 
    table2?: string
  ): {
    score: number;
    confidence: 'high' | 'medium' | 'low';
    matchedPatterns: string[];
    evidence: string[];
  } {
    const evidence: string[] = [];
    const matchedPatterns: string[] = [];
    let totalScore = 0;

    // 1. 完全匹配
    if (this.exactMatch(column1, column2)) {
      totalScore += 0.9;
      matchedPatterns.push('exact_match');
      evidence.push('列名完全匹配');
    }

    // 2. 主键-外键模式匹配
    const pkFkScore = this.primaryKeyForeignKeyMatch(column1, column2, table1, table2);
    if (pkFkScore > 0) {
      totalScore += pkFkScore;
      matchedPatterns.push('pk_fk_pattern');
      evidence.push('主键-外键命名模式');
    }

    // 3. 表名前缀模式匹配
    const prefixScore = this.tablePrefixMatch(column1, column2, table1, table2);
    if (prefixScore > 0) {
      totalScore += prefixScore;
      matchedPatterns.push('table_prefix');
      evidence.push('表名前缀模式');
    }

    // 4. 常见后缀模式匹配
    const suffixScore = this.commonSuffixMatch(column1, column2);
    if (suffixScore > 0) {
      totalScore += suffixScore;
      matchedPatterns.push('common_suffix');
      evidence.push('常见后缀模式');
    }

    // 5. 复数/单数转换匹配
    const pluralScore = this.pluralSingularMatch(column1, column2);
    if (pluralScore > 0) {
      totalScore += pluralScore;
      matchedPatterns.push('plural_singular');
      evidence.push('复数/单数转换');
    }

    // 6. 命名风格转换匹配
    const styleScore = this.namingStyleMatch(column1, column2);
    if (styleScore > 0) {
      totalScore += styleScore;
      matchedPatterns.push('naming_style');
      evidence.push('命名风格转换');
    }

    // 7. 模糊匹配（编辑距离）
    const fuzzyScore = this.fuzzyMatch(column1, column2);
    if (fuzzyScore > 0) {
      totalScore += fuzzyScore;
      matchedPatterns.push('fuzzy_match');
      evidence.push(`模糊匹配 (相似度: ${(fuzzyScore * 100).toFixed(1)}%)`);
    }

    // 8. 语义相似性匹配
    const semanticScore = this.semanticMatch(column1, column2);
    if (semanticScore > 0) {
      totalScore += semanticScore;
      matchedPatterns.push('semantic_match');
      evidence.push('语义相似性');
    }

    // 限制最大分数
    const finalScore = Math.min(1.0, totalScore);

    return {
      score: finalScore,
      confidence: this.scoreToConfidence(finalScore),
      matchedPatterns,
      evidence
    };
  }

  /**
   * 完全匹配
   */
  private exactMatch(column1: string, column2: string): boolean {
    return column1.toLowerCase() === column2.toLowerCase();
  }

  /**
   * 主键-外键模式匹配
   */
  private primaryKeyForeignKeyMatch(
    column1: string, 
    column2: string, 
    table1?: string, 
    table2?: string
  ): number {
    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();

    // id <-> table_id 模式
    if (lower1 === 'id' && lower2.endsWith('_id')) {
      const prefix = lower2.replace('_id', '');
      if (table1 && table1.toLowerCase().includes(prefix)) {
        return 0.8;
      }
      if (this.tableNames.has(prefix)) {
        return 0.7;
      }
      return 0.6;
    }

    if (lower2 === 'id' && lower1.endsWith('_id')) {
      const prefix = lower1.replace('_id', '');
      if (table2 && table2.toLowerCase().includes(prefix)) {
        return 0.8;
      }
      if (this.tableNames.has(prefix)) {
        return 0.7;
      }
      return 0.6;
    }

    // table1_id <-> table2_id 模式
    if (lower1.endsWith('_id') && lower2.endsWith('_id')) {
      const base1 = lower1.replace('_id', '');
      const base2 = lower2.replace('_id', '');
      if (base1 === base2) {
        return 0.9;
      }
    }

    return 0;
  }

  /**
   * 表名前缀模式匹配
   */
  private tablePrefixMatch(
    column1: string, 
    column2: string, 
    table1?: string, 
    table2?: string
  ): number {
    if (!table1 || !table2) return 0;

    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();
    const lowerTable1 = table1.toLowerCase();
    const lowerTable2 = table2.toLowerCase();

    // table1.column <-> table2.column 模式
    const base1 = lower1.replace(`${lowerTable1}_`, '');
    const base2 = lower2.replace(`${lowerTable2}_`, '');

    if (base1 === base2 && base1 !== lower1 && base2 !== lower2) {
      return 0.7;
    }

    return 0;
  }

  /**
   * 常见后缀模式匹配
   */
  private commonSuffixMatch(column1: string, column2: string): number {
    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();

    const suffixPairs = [
      ['_id', '_key'],
      ['_id', '_ref'],
      ['_key', '_ref'],
      ['_code', '_id'],
      ['_num', '_number'],
      ['_dt', '_date'],
      ['_ts', '_timestamp']
    ];

    for (const [suffix1, suffix2] of suffixPairs) {
      if ((lower1.endsWith(suffix1) && lower2.endsWith(suffix2)) ||
          (lower1.endsWith(suffix2) && lower2.endsWith(suffix1))) {
        const base1 = lower1.replace(new RegExp(`${suffix1}$|${suffix2}$`), '');
        const base2 = lower2.replace(new RegExp(`${suffix1}$|${suffix2}$`), '');
        if (base1 === base2) {
          return 0.6;
        }
      }
    }

    return 0;
  }

  /**
   * 复数/单数转换匹配
   */
  private pluralSingularMatch(column1: string, column2: string): number {
    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();

    // 简单的复数规则
    const pluralRules = [
      (s: string) => s + 's',
      (s: string) => s + 'es',
      (s: string) => s.replace(/y$/, 'ies'),
      (s: string) => s.replace(/f$/, 'ves'),
      (s: string) => s.replace(/fe$/, 'ves')
    ];

    for (const rule of pluralRules) {
      if (rule(lower1) === lower2 || rule(lower2) === lower1) {
        return 0.5;
      }
    }

    return 0;
  }

  /**
   * 命名风格转换匹配
   */
  private namingStyleMatch(column1: string, column2: string): number {
    // snake_case <-> camelCase 转换
    const snakeToCase = (str: string) => 
      str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    
    const camelToSnake = (str: string) => 
      str.replace(/([A-Z])/g, '_$1').toLowerCase();

    if (snakeToCase(column1.toLowerCase()) === column2.toLowerCase() ||
        camelToSnake(column1) === column2.toLowerCase()) {
      return 0.6;
    }

    return 0;
  }

  /**
   * 模糊匹配（基于编辑距离）
   */
  private fuzzyMatch(column1: string, column2: string): number {
    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();

    if (lower1 === lower2) return 0; // 已经在完全匹配中处理

    const maxLength = Math.max(lower1.length, lower2.length);
    const distance = this.levenshteinDistance(lower1, lower2);
    const similarity = 1 - (distance / maxLength);

    // 只有相似度较高时才认为是匹配
    return similarity > 0.7 ? similarity * 0.4 : 0;
  }

  /**
   * 语义相似性匹配
   */
  private semanticMatch(column1: string, column2: string): number {
    const synonymGroups = [
      ['id', 'key', 'ref', 'reference'],
      ['name', 'title', 'label'],
      ['desc', 'description', 'comment'],
      ['date', 'time', 'timestamp', 'dt', 'ts'],
      ['num', 'number', 'count', 'cnt'],
      ['code', 'cd', 'abbr', 'abbreviation'],
      ['status', 'state', 'flag'],
      ['type', 'kind', 'category', 'cat'],
      ['amount', 'amt', 'value', 'val'],
      ['address', 'addr', 'location', 'loc']
    ];

    const lower1 = column1.toLowerCase();
    const lower2 = column2.toLowerCase();

    for (const group of synonymGroups) {
      if (group.includes(lower1) && group.includes(lower2)) {
        return 0.5;
      }
    }

    return 0;
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * 将分数转换为置信度等级
   */
  private scoreToConfidence(score: number): 'high' | 'medium' | 'low' {
    if (score >= 0.8) return 'high';
    if (score >= 0.5) return 'medium';
    return 'low';
  }
}
