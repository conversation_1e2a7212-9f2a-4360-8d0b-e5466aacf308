import Joi from 'joi';
import { DataSourceType, RelationshipType } from '../types';

// 数据源验证Schema
export const dataSourceSchema = Joi.object({
  name: Joi.string().min(1).max(255).required().messages({
    'string.empty': '数据源名称不能为空',
    'string.max': '数据源名称不能超过255个字符'
  }),
  type: Joi.string().valid('postgresql', 'mysql', 'csv', 'excel').required().messages({
    'any.only': '数据源类型必须是 postgresql, mysql, csv 或 excel 之一'
  }),
  connectionConfig: Joi.object().required().messages({
    'object.base': '连接配置必须是一个对象'
  })
});

// 数据库连接配置验证Schema
export const databaseConnectionSchema = Joi.object({
  host: Joi.string().required().messages({
    'string.empty': '主机地址不能为空'
  }),
  port: Joi.number().integer().min(1).max(65535).required().messages({
    'number.base': '端口必须是数字',
    'number.min': '端口必须大于0',
    'number.max': '端口必须小于65536'
  }),
  database: Joi.string().required().messages({
    'string.empty': '数据库名称不能为空'
  }),
  username: Joi.string().required().messages({
    'string.empty': '用户名不能为空'
  }),
  password: Joi.string().required().messages({
    'string.empty': '密码不能为空'
  }),
  ssl: Joi.boolean().optional()
});

// 文件连接配置验证Schema
export const fileConnectionSchema = Joi.object({
  filename: Joi.string().required().messages({
    'string.empty': '文件名不能为空'
  }),
  path: Joi.string().required().messages({
    'string.empty': '文件路径不能为空'
  }),
  encoding: Joi.string().optional(),
  delimiter: Joi.string().optional(),
  sheetName: Joi.string().optional()
});

// 聊天请求验证Schema
export const chatRequestSchema = Joi.object({
  sessionId: Joi.string().required().messages({
    'string.empty': '会话ID不能为空'
  }),
  prompt: Joi.string().min(1).max(2000).required().messages({
    'string.empty': '问题不能为空',
    'string.max': '问题不能超过2000个字符'
  })
});

// 数据表别名更新验证Schema
export const tableAliasUpdateSchema = Joi.object({
  aliasName: Joi.string().max(255).optional().allow(''),
  description: Joi.string().max(1000).optional().allow('')
});

// 数据列别名更新验证Schema
export const columnAliasUpdateSchema = Joi.object({
  aliasName: Joi.string().max(255).optional().allow(''),
  description: Joi.string().max(1000).optional().allow('')
});

// 关联关系创建验证Schema
export const relationshipCreateSchema = Joi.object({
  fromColumnId: Joi.string().uuid().required().messages({
    'string.empty': '起始列ID不能为空',
    'string.guid': '起始列ID格式无效'
  }),
  toColumnId: Joi.string().uuid().required().messages({
    'string.empty': '目标列ID不能为空',
    'string.guid': '目标列ID格式无效'
  }),
  relationshipType: Joi.string().valid('one_to_one', 'one_to_many', 'many_to_one').required().messages({
    'any.only': '关联类型必须是 one_to_one, one_to_many 或 many_to_one 之一'
  })
});

/**
 * 验证数据源配置
 */
export function validateDataSourceConfig(type: DataSourceType, config: any) {
  if (type === 'postgresql' || type === 'mysql') {
    const { error } = databaseConnectionSchema.validate(config);
    if (error) {
      throw new Error(`数据库连接配置验证失败: ${error.details[0].message}`);
    }
  } else if (type === 'csv' || type === 'excel') {
    const { error } = fileConnectionSchema.validate(config);
    if (error) {
      throw new Error(`文件连接配置验证失败: ${error.details[0].message}`);
    }
  }
}

/**
 * 通用验证函数
 */
export function validate(schema: Joi.ObjectSchema, data: any) {
  const { error, value } = schema.validate(data, { abortEarly: false });
  
  if (error) {
    const errorMessages = error.details.map(detail => detail.message);
    throw new Error(errorMessages.join('; '));
  }
  
  return value;
}
