{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/services/*": ["services/*"], "@/controllers/*": ["controllers/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"]}}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}