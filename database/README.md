# AI-BI 数据库脚本说明

## 概述

本目录包含 AI-BI 智能商业分析系统的数据库初始化、种子数据和维护脚本。这些脚本设计用于 PostgreSQL 数据库，提供完整的数据库结构、示例数据和维护功能。

## 脚本文件说明

### 1. `01-init.sql` - 数据库初始化脚本
**主要功能：**
- 创建所有必要的数据库表结构
- 设置索引以优化查询性能
- 创建触发器和函数
- 建立数据完整性约束
- 创建系统视图

**包含的表：**
- `data_sources` - 数据源管理
- `data_tables` - 数据表元数据
- `data_columns` - 数据列元数据
- `data_relationships` - 表间关联关系
- `chat_history` - 对话历史记录
- `system_config` - 系统配置
- `query_templates` - 查询模板
- `data_quality_checks` - 数据质量检查
- `user_sessions` - 用户会话
- `system_logs` - 系统日志

### 2. `02-seed-data.sql` - 种子数据脚本
**主要功能：**
- 插入示例数据源配置
- 创建示例数据表和列
- 建立示例关联关系
- 添加示例对话历史
- 插入数据质量检查记录

**示例数据包括：**
- 3个不同类型的数据源（PostgreSQL、CSV、Excel）
- 5个数据表（客户、订单、产品等）
- 16个数据列（包含各种数据类型）
- 2个关联关系（客户-订单、产品-订单）
- 3条对话历史记录

### 3. `03-maintenance.sql` - 维护脚本
**主要功能：**
- 数据库清理函数
- 性能优化工具
- 数据完整性检查
- 系统健康监控
- 自动维护任务

**维护功能：**
- 清理过期会话和旧日志
- 更新表统计信息
- 检查数据完整性
- 系统健康检查
- 生成系统报告

## 使用方法

### 方法一：Docker 自动执行（推荐）
当使用 `docker-compose up` 启动系统时，这些脚本会自动执行：

```bash
# 启动整个系统（包括数据库初始化）
docker-compose up -d

# 查看数据库初始化日志
docker logs ai-bi-postgres
```

### 方法二：手动执行
如果需要手动执行脚本：

```bash
# 连接到 PostgreSQL 数据库
psql -h localhost -p 5432 -U ai_bi_user -d ai_bi_system

# 按顺序执行脚本
\i /docker-entrypoint-initdb.d/01-init.sql
\i /docker-entrypoint-initdb.d/02-seed-data.sql
\i /docker-entrypoint-initdb.d/03-maintenance.sql
```

### 方法三：使用 psql 命令行
```bash
# 执行初始化脚本
psql -h localhost -p 5432 -U ai_bi_user -d ai_bi_system -f database/init/01-init.sql

# 执行种子数据脚本
psql -h localhost -p 5432 -U ai_bi_user -d ai_bi_system -f database/init/02-seed-data.sql

# 执行维护脚本
psql -h localhost -p 5432 -U ai_bi_user -d ai_bi_system -f database/init/03-maintenance.sql
```

## 数据库结构概览

### 核心表关系
```
data_sources (数据源)
    ↓ 1:N
data_tables (数据表)
    ↓ 1:N
data_columns (数据列)
    ↓ N:N
data_relationships (关联关系)
```

### 功能表
- `chat_history` - 存储用户与AI的对话记录
- `system_config` - 系统配置参数
- `query_templates` - 常用查询模板
- `data_quality_checks` - 数据质量检查结果
- `user_sessions` - 用户会话管理
- `system_logs` - 系统操作日志

## 维护任务

### 每日维护任务
```sql
-- 执行每日维护
SELECT * FROM run_daily_maintenance();
```

包括：
- 清理过期会话
- 清理旧日志
- 更新表统计信息

### 周度维护任务
```sql
-- 执行周度维护
SELECT * FROM run_weekly_maintenance();
```

包括：
- 数据完整性检查
- 系统健康检查
- 清理旧对话历史

### 系统监控
```sql
-- 生成系统报告
SELECT * FROM generate_system_report();

-- 检查系统健康状态
SELECT * FROM system_health_check();

-- 检查数据完整性
SELECT * FROM check_data_integrity();
```

## 性能优化

### 索引策略
- 为所有外键创建索引
- 为常用查询字段创建复合索引
- 使用 GIN 索引支持全文搜索
- 为 JSONB 字段创建 GIN 索引

### 查询优化
- 使用视图简化复杂查询
- 创建统计信息收集策略
- 定期更新表统计信息

## 安全考虑

### 数据加密
- 敏感配置信息使用 `pgcrypto` 扩展加密
- 连接配置信息在存储前加密

### 访问控制
- 支持创建只读用户
- 使用行级安全策略（可扩展）

### 审计日志
- 所有系统操作记录到 `system_logs` 表
- 支持不同级别的日志记录

## 故障排除

### 常见问题

1. **脚本执行失败**
   ```bash
   # 检查 PostgreSQL 日志
   docker logs ai-bi-postgres
   
   # 检查数据库连接
   psql -h localhost -p 5432 -U ai_bi_user -d ai_bi_system -c "SELECT version();"
   ```

2. **权限问题**
   ```sql
   -- 检查用户权限
   SELECT * FROM information_schema.role_table_grants WHERE grantee = 'ai_bi_user';
   ```

3. **表不存在**
   ```sql
   -- 检查表是否创建成功
   SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';
   ```

### 重新初始化
如果需要重新初始化数据库：

```bash
# 停止服务
docker-compose down

# 删除数据卷
docker volume rm ai-bi_postgres_data

# 重新启动
docker-compose up -d
```

## 扩展和定制

### 添加新表
1. 在 `01-init.sql` 中添加表结构
2. 在 `02-seed-data.sql` 中添加示例数据
3. 更新相关的视图和函数

### 修改维护策略
1. 编辑 `03-maintenance.sql` 中的维护函数
2. 更新 `system_config` 表中的维护配置

### 性能调优
1. 根据实际使用情况调整索引
2. 修改统计信息收集策略
3. 调整清理任务的执行频率

## 版本信息

- **版本**: 1.0
- **创建时间**: 2024-12-01
- **兼容性**: PostgreSQL 12+
- **依赖扩展**: uuid-ossp, pgcrypto, btree_gin, pg_trgm

## 联系和支持

如有问题或建议，请查看项目文档或提交 Issue。
