-- =====================================================
-- AI-BI 系统数据库初始化脚本
-- 版本: 1.0
-- 创建时间: 2024-12-01
-- 描述: 初始化 AI-BI 智能商业分析系统的数据库结构
-- =====================================================

-- 创建数据库（如果不存在）
-- CREATE DATABASE ai_bi_system;

-- 创建必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";        -- UUID 生成函数
CREATE EXTENSION IF NOT EXISTS "pgcrypto";         -- 加密函数
CREATE EXTENSION IF NOT EXISTS "btree_gin";        -- GIN 索引支持
CREATE EXTENSION IF NOT EXISTS "pg_trgm";          -- 文本相似度搜索

-- 设置时区和编码
SET timezone = 'UTC';
SET client_encoding = 'UTF8';

-- =====================================================
-- 1. 数据源表 (data_sources)
-- 存储用户连接的外部数据源信息
-- =====================================================
CREATE TABLE IF NOT EXISTS data_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('postgresql', 'mysql', 'csv', 'excel', 'sqlite', 'mongodb')),
    connection_config TEXT NOT NULL, -- 加密后的连接配置信息 (JSON 格式)
    status VARCHAR(20) DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'error', 'testing')),
    last_sync_at TIMESTAMPTZ,
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为数据源表创建索引
CREATE INDEX IF NOT EXISTS idx_data_sources_type ON data_sources(type);
CREATE INDEX IF NOT EXISTS idx_data_sources_status ON data_sources(status);
CREATE INDEX IF NOT EXISTS idx_data_sources_name ON data_sources USING GIN(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_data_sources_created_at ON data_sources(created_at);

-- =====================================================
-- 2. 数据表元数据表 (data_tables)
-- 缓存并管理从外部数据源同步过来的表的元数据
-- =====================================================
CREATE TABLE IF NOT EXISTS data_tables (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data_source_id UUID NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,
    original_table_name VARCHAR(255) NOT NULL,
    alias_name VARCHAR(255),
    description TEXT,
    is_synced BOOLEAN NOT NULL DEFAULT FALSE,
    row_count BIGINT DEFAULT 0,
    last_analyzed_at TIMESTAMPTZ,
    table_size_bytes BIGINT DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- 确保同一数据源下的表名唯一
    UNIQUE(data_source_id, original_table_name)
);

-- 为数据表元数据表创建索引
CREATE INDEX IF NOT EXISTS idx_data_tables_data_source_id ON data_tables(data_source_id);
CREATE INDEX IF NOT EXISTS idx_data_tables_original_name ON data_tables(original_table_name);
CREATE INDEX IF NOT EXISTS idx_data_tables_alias_name ON data_tables USING GIN(alias_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_data_tables_is_synced ON data_tables(is_synced);
CREATE INDEX IF NOT EXISTS idx_data_tables_updated_at ON data_tables(updated_at);

-- =====================================================
-- 3. 数据列元数据表 (data_columns)
-- 缓存并管理数据表的列（字段）的元数据
-- =====================================================
CREATE TABLE IF NOT EXISTS data_columns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data_table_id UUID NOT NULL REFERENCES data_tables(id) ON DELETE CASCADE,
    original_column_name VARCHAR(255) NOT NULL,
    original_data_type VARCHAR(100) NOT NULL,
    alias_name VARCHAR(255),
    description TEXT,
    is_primary_key BOOLEAN NOT NULL DEFAULT FALSE,
    is_foreign_key BOOLEAN NOT NULL DEFAULT FALSE,
    is_nullable BOOLEAN NOT NULL DEFAULT TRUE,
    default_value TEXT,
    max_length INTEGER,
    precision_value INTEGER,
    scale_value INTEGER,
    is_indexed BOOLEAN NOT NULL DEFAULT FALSE,
    unique_values_count BIGINT DEFAULT 0,
    null_values_count BIGINT DEFAULT 0,
    sample_values JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- 确保同一表下的列名唯一
    UNIQUE(data_table_id, original_column_name)
);

-- 为数据列元数据表创建索引
CREATE INDEX IF NOT EXISTS idx_data_columns_data_table_id ON data_columns(data_table_id);
CREATE INDEX IF NOT EXISTS idx_data_columns_original_name ON data_columns(original_column_name);
CREATE INDEX IF NOT EXISTS idx_data_columns_alias_name ON data_columns USING GIN(alias_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_data_columns_data_type ON data_columns(original_data_type);
CREATE INDEX IF NOT EXISTS idx_data_columns_is_primary_key ON data_columns(is_primary_key);
CREATE INDEX IF NOT EXISTS idx_data_columns_is_foreign_key ON data_columns(is_foreign_key);
CREATE INDEX IF NOT EXISTS idx_data_columns_sample_values ON data_columns USING GIN(sample_values);

-- =====================================================
-- 4. 数据关联关系表 (data_relationships)
-- 存储表之间的关联关系，由系统自动发现或用户手动创建
-- =====================================================
CREATE TABLE IF NOT EXISTS data_relationships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_column_id UUID NOT NULL REFERENCES data_columns(id) ON DELETE CASCADE,
    to_column_id UUID NOT NULL REFERENCES data_columns(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL CHECK (relationship_type IN ('one_to_one', 'one_to_many', 'many_to_one', 'many_to_many')),
    is_manual BOOLEAN NOT NULL DEFAULT FALSE,

    -- 置信度相关字段
    discovery_method VARCHAR(50) CHECK (discovery_method IN ('foreign_key', 'naming_convention', 'data_analysis', 'manual', 'ai_suggested')),
    confidence VARCHAR(20) CHECK (confidence IN ('high', 'medium', 'low')),
    confidence_score REAL CHECK (confidence_score >= 0 AND confidence_score <= 1),
    evidence TEXT[], -- 支持该关联关系的证据
    metadata JSONB, -- 额外的元数据信息

    -- 验证状态
    is_validated BOOLEAN NOT NULL DEFAULT FALSE,
    validation_date TIMESTAMPTZ,
    validation_method VARCHAR(50),

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- 防止重复的关联关系
    UNIQUE(from_column_id, to_column_id),
    -- 防止自关联
    CHECK (from_column_id != to_column_id)
);

-- 为数据关联关系表创建索引
CREATE INDEX IF NOT EXISTS idx_data_relationships_from_column ON data_relationships(from_column_id);
CREATE INDEX IF NOT EXISTS idx_data_relationships_to_column ON data_relationships(to_column_id);
CREATE INDEX IF NOT EXISTS idx_data_relationships_type ON data_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_data_relationships_is_manual ON data_relationships(is_manual);
CREATE INDEX IF NOT EXISTS idx_data_relationships_confidence ON data_relationships(confidence);
CREATE INDEX IF NOT EXISTS idx_data_relationships_discovery_method ON data_relationships(discovery_method);
CREATE INDEX IF NOT EXISTS idx_data_relationships_is_validated ON data_relationships(is_validated);
CREATE INDEX IF NOT EXISTS idx_data_relationships_metadata ON data_relationships USING GIN(metadata);

-- =====================================================
-- 5. 对话历史记录表 (chat_history)
-- 存储用户与系统的自然语言交互记录
-- =====================================================
CREATE TABLE IF NOT EXISTS chat_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL,
    user_query TEXT NOT NULL,
    generated_sql TEXT,
    query_result_data JSONB,
    natural_language_response TEXT,
    visualization_spec JSONB,

    -- 查询执行信息
    execution_time_ms INTEGER,
    result_row_count INTEGER,
    query_status VARCHAR(20) DEFAULT 'pending' CHECK (query_status IN ('pending', 'executing', 'completed', 'failed')),
    error_message TEXT,

    -- AI 模型信息
    model_used VARCHAR(100),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,

    -- 用户反馈
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    user_feedback TEXT,

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为对话历史记录表创建索引
CREATE INDEX IF NOT EXISTS idx_chat_history_session_id ON chat_history(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_created_at ON chat_history(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_history_query_status ON chat_history(query_status);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_rating ON chat_history(user_rating);
CREATE INDEX IF NOT EXISTS idx_chat_history_model_used ON chat_history(model_used);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_query ON chat_history USING GIN(to_tsvector('english', user_query));
CREATE INDEX IF NOT EXISTS idx_chat_history_visualization_spec ON chat_history USING GIN(visualization_spec);

-- =====================================================
-- 6. 系统配置表 (system_config)
-- 存储系统级别的配置信息
-- =====================================================
CREATE TABLE IF NOT EXISTS system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为系统配置表创建索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_updated_at ON system_config(updated_at);

-- =====================================================
-- 7. 查询模板表 (query_templates)
-- 存储常用的查询模板，提高查询效率
-- =====================================================
CREATE TABLE IF NOT EXISTS query_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_text TEXT NOT NULL,
    sql_template TEXT NOT NULL,
    category VARCHAR(100),
    tags TEXT[],
    usage_count INTEGER DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为查询模板表创建索引
CREATE INDEX IF NOT EXISTS idx_query_templates_name ON query_templates USING GIN(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_query_templates_category ON query_templates(category);
CREATE INDEX IF NOT EXISTS idx_query_templates_tags ON query_templates USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_query_templates_is_active ON query_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_query_templates_usage_count ON query_templates(usage_count DESC);

-- =====================================================
-- 8. 数据质量检查表 (data_quality_checks)
-- 存储数据质量检查的结果
-- =====================================================
CREATE TABLE IF NOT EXISTS data_quality_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data_source_id UUID REFERENCES data_sources(id) ON DELETE CASCADE,
    data_table_id UUID REFERENCES data_tables(id) ON DELETE CASCADE,
    data_column_id UUID REFERENCES data_columns(id) ON DELETE CASCADE,
    check_type VARCHAR(50) NOT NULL CHECK (check_type IN ('completeness', 'uniqueness', 'validity', 'consistency', 'accuracy')),
    check_name VARCHAR(255) NOT NULL,
    check_description TEXT,
    check_rule JSONB NOT NULL,
    result_status VARCHAR(20) NOT NULL CHECK (result_status IN ('passed', 'failed', 'warning', 'error')),
    result_value NUMERIC,
    threshold_value NUMERIC,
    result_details JSONB,
    execution_time_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为数据质量检查表创建索引
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_data_source_id ON data_quality_checks(data_source_id);
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_data_table_id ON data_quality_checks(data_table_id);
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_data_column_id ON data_quality_checks(data_column_id);
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_check_type ON data_quality_checks(check_type);
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_result_status ON data_quality_checks(result_status);
CREATE INDEX IF NOT EXISTS idx_data_quality_checks_created_at ON data_quality_checks(created_at);

-- =====================================================
-- 9. 用户会话表 (user_sessions)
-- 存储用户会话信息（为未来的用户管理做准备）
-- =====================================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    user_id VARCHAR(100), -- 预留用户ID字段
    ip_address INET,
    user_agent TEXT,
    session_data JSONB,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_activity_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为用户会话表创建索引
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_last_activity ON user_sessions(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- =====================================================
-- 10. 系统日志表 (system_logs)
-- 存储系统操作日志
-- =====================================================
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_level VARCHAR(20) NOT NULL CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL')),
    component VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    user_id VARCHAR(100),
    session_id VARCHAR(255),
    ip_address INET,
    execution_time_ms INTEGER,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 为系统日志表创建索引
CREATE INDEX IF NOT EXISTS idx_system_logs_log_level ON system_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_system_logs_component ON system_logs(component);
CREATE INDEX IF NOT EXISTS idx_system_logs_action ON system_logs(action);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_session_id ON system_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_message ON system_logs USING GIN(to_tsvector('english', message));

-- =====================================================
-- 11. 触发器函数
-- 自动更新 updated_at 字段
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新 updated_at 的表创建触发器
CREATE TRIGGER update_data_sources_updated_at
    BEFORE UPDATE ON data_sources
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_tables_updated_at
    BEFORE UPDATE ON data_tables
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_columns_updated_at
    BEFORE UPDATE ON data_columns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at
    BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_templates_updated_at
    BEFORE UPDATE ON query_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 12. 数据验证函数
-- 验证连接配置的JSON格式
-- =====================================================
CREATE OR REPLACE FUNCTION validate_connection_config(config_text TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- 尝试解析JSON
    PERFORM config_text::JSON;
    RETURN TRUE;
EXCEPTION
    WHEN invalid_text_representation THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- 为数据源表添加连接配置验证约束
ALTER TABLE data_sources
ADD CONSTRAINT check_connection_config_json
CHECK (validate_connection_config(connection_config));

-- =====================================================
-- 13. 统计函数
-- 获取数据源统计信息
-- =====================================================
CREATE OR REPLACE FUNCTION get_data_source_stats(source_id UUID)
RETURNS TABLE(
    table_count BIGINT,
    column_count BIGINT,
    relationship_count BIGINT,
    last_sync_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM data_tables WHERE data_source_id = source_id),
        (SELECT COUNT(*) FROM data_columns dc
         JOIN data_tables dt ON dc.data_table_id = dt.id
         WHERE dt.data_source_id = source_id),
        (SELECT COUNT(*) FROM data_relationships dr
         JOIN data_columns dc1 ON dr.from_column_id = dc1.id
         JOIN data_tables dt1 ON dc1.data_table_id = dt1.id
         WHERE dt1.data_source_id = source_id),
        (SELECT last_sync_at FROM data_sources WHERE id = source_id);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 14. 清理函数
-- 清理过期的会话和日志
-- =====================================================
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions
    WHERE expires_at < NOW() OR (last_activity_at < NOW() - INTERVAL '7 days');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION cleanup_old_logs(days_to_keep INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM system_logs
    WHERE created_at < NOW() - (days_to_keep || ' days')::INTERVAL;

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 15. 初始化系统配置数据
-- 插入默认的系统配置
-- =====================================================
INSERT INTO system_config (config_key, config_value, description, is_encrypted) VALUES
('ai_model_config', '{"model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 2000}', 'AI模型配置参数', false),
('query_timeout', '{"timeout_seconds": 30}', '查询超时时间配置', false),
('cache_config', '{"ttl_seconds": 1800, "max_size": 1000}', '缓存配置参数', false),
('data_sync_config', '{"auto_sync": true, "sync_interval_hours": 24}', '数据同步配置', false),
('security_config', '{"encryption_algorithm": "AES-256", "session_timeout_hours": 8}', '安全配置参数', true),
('visualization_config', '{"default_chart_type": "auto", "max_data_points": 10000}', '可视化配置参数', false),
('system_limits', '{"max_data_sources": 50, "max_tables_per_source": 500, "max_columns_per_table": 200}', '系统限制配置', false)
ON CONFLICT (config_key) DO NOTHING;

-- =====================================================
-- 16. 初始化查询模板数据
-- 插入常用的查询模板
-- =====================================================
INSERT INTO query_templates (name, description, template_text, sql_template, category, tags) VALUES
('销售总额查询', '查询指定时间段的销售总额', '查询{时间段}的销售总额', 'SELECT SUM(amount) as total_sales FROM sales WHERE date BETWEEN {start_date} AND {end_date}', '销售分析', ARRAY['销售', '总额', '时间段']),
('客户排行榜', '查询销售额最高的客户', '查询销售额最高的{数量}个客户', 'SELECT customer_name, SUM(amount) as total_amount FROM sales GROUP BY customer_name ORDER BY total_amount DESC LIMIT {limit}', '客户分析', ARRAY['客户', '排行榜', '销售额']),
('产品销量统计', '统计产品销量情况', '统计{产品}的销量情况', 'SELECT product_name, COUNT(*) as sales_count, SUM(amount) as total_amount FROM sales WHERE product_name LIKE ''%{product}%'' GROUP BY product_name', '产品分析', ARRAY['产品', '销量', '统计']),
('月度趋势分析', '分析月度销售趋势', '分析{年份}年的月度销售趋势', 'SELECT EXTRACT(MONTH FROM date) as month, SUM(amount) as monthly_sales FROM sales WHERE EXTRACT(YEAR FROM date) = {year} GROUP BY EXTRACT(MONTH FROM date) ORDER BY month', '趋势分析', ARRAY['月度', '趋势', '销售']),
('数据质量检查', '检查数据完整性', '检查{表名}的数据完整性', 'SELECT COUNT(*) as total_rows, COUNT({column}) as non_null_rows, (COUNT({column}) * 100.0 / COUNT(*)) as completeness_rate FROM {table}', '数据质量', ARRAY['数据质量', '完整性', '检查'])
ON CONFLICT DO NOTHING;

-- =====================================================
-- 17. 创建视图
-- 创建常用的数据视图
-- =====================================================

-- 数据源概览视图
CREATE OR REPLACE VIEW v_data_source_overview AS
SELECT
    ds.id,
    ds.name,
    ds.type,
    ds.status,
    ds.last_sync_at,
    COUNT(DISTINCT dt.id) as table_count,
    COUNT(DISTINCT dc.id) as column_count,
    COUNT(DISTINCT dr.id) as relationship_count,
    ds.created_at,
    ds.updated_at
FROM data_sources ds
LEFT JOIN data_tables dt ON ds.id = dt.data_source_id
LEFT JOIN data_columns dc ON dt.id = dc.data_table_id
LEFT JOIN data_relationships dr ON dc.id IN (dr.from_column_id, dr.to_column_id)
GROUP BY ds.id, ds.name, ds.type, ds.status, ds.last_sync_at, ds.created_at, ds.updated_at;

-- 表详情视图
CREATE OR REPLACE VIEW v_table_details AS
SELECT
    dt.id,
    dt.data_source_id,
    ds.name as data_source_name,
    dt.original_table_name,
    dt.alias_name,
    dt.description,
    dt.is_synced,
    dt.row_count,
    COUNT(dc.id) as column_count,
    COUNT(CASE WHEN dc.is_primary_key THEN 1 END) as primary_key_count,
    COUNT(CASE WHEN dc.is_foreign_key THEN 1 END) as foreign_key_count,
    dt.last_analyzed_at,
    dt.created_at,
    dt.updated_at
FROM data_tables dt
JOIN data_sources ds ON dt.data_source_id = ds.id
LEFT JOIN data_columns dc ON dt.id = dc.data_table_id
GROUP BY dt.id, dt.data_source_id, ds.name, dt.original_table_name,
         dt.alias_name, dt.description, dt.is_synced, dt.row_count,
         dt.last_analyzed_at, dt.created_at, dt.updated_at;

-- 关联关系详情视图
CREATE OR REPLACE VIEW v_relationship_details AS
SELECT
    dr.id,
    dr.relationship_type,
    dr.is_manual,
    dr.discovery_method,
    dr.confidence,
    dr.confidence_score,

    -- 起始列信息
    fc.original_column_name as from_column_name,
    fc.alias_name as from_column_alias,
    ft.original_table_name as from_table_name,
    ft.alias_name as from_table_alias,
    fs.name as from_source_name,

    -- 目标列信息
    tc.original_column_name as to_column_name,
    tc.alias_name as to_column_alias,
    tt.original_table_name as to_table_name,
    tt.alias_name as to_table_alias,
    ts.name as to_source_name,

    dr.created_at
FROM data_relationships dr
JOIN data_columns fc ON dr.from_column_id = fc.id
JOIN data_tables ft ON fc.data_table_id = ft.id
JOIN data_sources fs ON ft.data_source_id = fs.id
JOIN data_columns tc ON dr.to_column_id = tc.id
JOIN data_tables tt ON tc.data_table_id = tt.id
JOIN data_sources ts ON tt.data_source_id = ts.id;

-- =====================================================
-- 18. 性能优化设置
-- 设置数据库性能参数
-- =====================================================

-- 设置统计信息收集
ALTER TABLE data_sources SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE data_tables SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE data_columns SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE data_relationships SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE chat_history SET (autovacuum_analyze_scale_factor = 0.1);

-- 更新表统计信息
ANALYZE data_sources;
ANALYZE data_tables;
ANALYZE data_columns;
ANALYZE data_relationships;
ANALYZE chat_history;
ANALYZE system_config;
ANALYZE query_templates;
ANALYZE data_quality_checks;
ANALYZE user_sessions;
ANALYZE system_logs;

-- =====================================================
-- 19. 权限设置
-- 设置数据库用户权限（如果需要）
-- =====================================================

-- 创建只读用户（可选）
-- CREATE USER ai_bi_readonly WITH PASSWORD 'readonly_password';
-- GRANT CONNECT ON DATABASE ai_bi_system TO ai_bi_readonly;
-- GRANT USAGE ON SCHEMA public TO ai_bi_readonly;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO ai_bi_readonly;
-- GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO ai_bi_readonly;

-- =====================================================
-- 20. 数据库完整性检查
-- 验证数据库结构的完整性
-- =====================================================

-- 检查所有表是否创建成功
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY[
        'data_sources', 'data_tables', 'data_columns', 'data_relationships',
        'chat_history', 'system_config', 'query_templates', 'data_quality_checks',
        'user_sessions', 'system_logs'
    ];
    table_name TEXT;
BEGIN
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = table_name;

        IF table_count = 0 THEN
            RAISE EXCEPTION '表 % 创建失败', table_name;
        END IF;
    END LOOP;

    RAISE NOTICE '所有数据库表创建成功！';
END $$;

-- 检查视图是否创建成功
DO $$
DECLARE
    view_count INTEGER;
    expected_views TEXT[] := ARRAY[
        'v_data_source_overview', 'v_table_details', 'v_relationship_details'
    ];
    view_name TEXT;
BEGIN
    FOREACH view_name IN ARRAY expected_views
    LOOP
        SELECT COUNT(*) INTO view_count
        FROM information_schema.views
        WHERE table_schema = 'public' AND table_name = view_name;

        IF view_count = 0 THEN
            RAISE EXCEPTION '视图 % 创建失败', view_name;
        END IF;
    END LOOP;

    RAISE NOTICE '所有数据库视图创建成功！';
END $$;

-- =====================================================
-- 21. 初始化完成日志
-- 记录初始化完成信息
-- =====================================================

-- 插入初始化完成日志
INSERT INTO system_logs (log_level, component, action, message, details) VALUES
('INFO', 'DATABASE', 'INITIALIZATION', '数据库初始化完成',
 json_build_object(
     'version', '1.0',
     'timestamp', NOW(),
     'tables_created', 10,
     'views_created', 3,
     'functions_created', 5,
     'triggers_created', 5
 ));

-- 显示初始化完成信息
SELECT
    'AI-BI 数据库初始化完成！' as status,
    NOW() as completion_time,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') as total_tables,
    (SELECT COUNT(*) FROM information_schema.views WHERE table_schema = 'public') as total_views,
    (SELECT COUNT(*) FROM information_schema.routines WHERE routine_schema = 'public') as total_functions;

-- =====================================================
-- 脚本执行完成
-- =====================================================
\echo '=========================================='
\echo 'AI-BI 数据库初始化脚本执行完成！'
\echo '版本: 1.0'
\echo '创建时间: 2024-12-01'
\echo '=========================================='
