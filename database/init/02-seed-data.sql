-- =====================================================
-- AI-BI 系统种子数据脚本
-- 版本: 1.0
-- 创建时间: 2024-12-01
-- 描述: 为 AI-BI 系统插入示例数据，用于开发和测试
-- =====================================================

-- 设置时区
SET timezone = 'UTC';

-- =====================================================
-- 1. 示例数据源
-- =====================================================
INSERT INTO data_sources (id, name, type, connection_config, status, last_sync_at, sync_status) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    '生产数据库',
    'postgresql',
    '{"host": "localhost", "port": 5432, "database": "production", "username": "prod_user", "password": "encrypted_password", "ssl": true}',
    'active',
    NOW() - INTERVAL '2 hours',
    'completed'
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    '销售数据CSV',
    'csv',
    '{"file_path": "/data/sales_data.csv", "delimiter": ",", "encoding": "utf-8", "has_header": true}',
    'active',
    NOW() - INTERVAL '1 day',
    'completed'
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '客户调研Excel',
    'excel',
    '{"file_path": "/data/customer_survey.xlsx", "sheet_name": "Survey Results", "header_row": 1}',
    'inactive',
    NULL,
    'pending'
);

-- =====================================================
-- 2. 示例数据表
-- =====================================================
INSERT INTO data_tables (id, data_source_id, original_table_name, alias_name, description, is_synced, row_count, last_analyzed_at) VALUES
-- 生产数据库的表
(
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    'customers',
    '客户信息',
    '存储客户基本信息的表',
    true,
    15420,
    NOW() - INTERVAL '2 hours'
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    'orders',
    '订单信息',
    '存储订单详细信息的表',
    true,
    89650,
    NOW() - INTERVAL '2 hours'
),
(
    '660e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440001',
    'products',
    '产品信息',
    '存储产品基本信息的表',
    true,
    2340,
    NOW() - INTERVAL '2 hours'
),
-- CSV文件的表
(
    '660e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440002',
    'sales_data',
    '销售数据',
    'CSV格式的销售数据',
    true,
    45230,
    NOW() - INTERVAL '1 day'
),
-- Excel文件的表
(
    '660e8400-e29b-41d4-a716-446655440005',
    '550e8400-e29b-41d4-a716-446655440003',
    'survey_results',
    '调研结果',
    'Excel格式的客户调研结果',
    false,
    0,
    NULL
);

-- =====================================================
-- 3. 示例数据列
-- =====================================================
INSERT INTO data_columns (id, data_table_id, original_column_name, original_data_type, alias_name, description, is_primary_key, is_foreign_key, is_nullable, unique_values_count, sample_values) VALUES
-- customers表的列
(
    '770e8400-e29b-41d4-a716-446655440001',
    '660e8400-e29b-41d4-a716-446655440001',
    'customer_id',
    'integer',
    '客户ID',
    '客户的唯一标识符',
    true,
    false,
    false,
    15420,
    '["1", "2", "3", "4", "5"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440002',
    '660e8400-e29b-41d4-a716-446655440001',
    'customer_name',
    'varchar(255)',
    '客户姓名',
    '客户的姓名',
    false,
    false,
    false,
    15420,
    '["张三", "李四", "王五", "赵六", "钱七"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440003',
    '660e8400-e29b-41d4-a716-446655440001',
    'email',
    'varchar(255)',
    '邮箱地址',
    '客户的邮箱地址',
    false,
    false,
    true,
    15420,
    '["<EMAIL>", "<EMAIL>", "<EMAIL>"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440004',
    '660e8400-e29b-41d4-a716-446655440001',
    'phone',
    'varchar(20)',
    '电话号码',
    '客户的联系电话',
    false,
    false,
    true,
    15420,
    '["13800138000", "13900139000", "13700137000"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440005',
    '660e8400-e29b-41d4-a716-446655440001',
    'created_at',
    'timestamp',
    '创建时间',
    '客户记录的创建时间',
    false,
    false,
    false,
    15420,
    '["2024-01-01 10:00:00", "2024-01-02 11:00:00", "2024-01-03 12:00:00"]'::jsonb
),
-- orders表的列
(
    '770e8400-e29b-41d4-a716-446655440006',
    '660e8400-e29b-41d4-a716-446655440002',
    'order_id',
    'integer',
    '订单ID',
    '订单的唯一标识符',
    true,
    false,
    false,
    89650,
    '["1001", "1002", "1003", "1004", "1005"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440007',
    '660e8400-e29b-41d4-a716-446655440002',
    'customer_id',
    'integer',
    '客户ID',
    '关联的客户ID',
    false,
    true,
    false,
    15420,
    '["1", "2", "3", "4", "5"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440008',
    '660e8400-e29b-41d4-a716-446655440002',
    'product_id',
    'integer',
    '产品ID',
    '关联的产品ID',
    false,
    true,
    false,
    2340,
    '["101", "102", "103", "104", "105"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440009',
    '660e8400-e29b-41d4-a716-446655440002',
    'quantity',
    'integer',
    '数量',
    '订单中产品的数量',
    false,
    false,
    false,
    50,
    '["1", "2", "3", "5", "10"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440010',
    '660e8400-e29b-41d4-a716-446655440002',
    'unit_price',
    'decimal(10,2)',
    '单价',
    '产品的单价',
    false,
    false,
    false,
    1200,
    '["99.99", "199.99", "299.99", "399.99", "499.99"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440011',
    '660e8400-e29b-41d4-a716-446655440002',
    'total_amount',
    'decimal(12,2)',
    '总金额',
    '订单的总金额',
    false,
    false,
    false,
    25000,
    '["99.99", "399.98", "899.97", "1999.95", "4999.90"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440012',
    '660e8400-e29b-41d4-a716-446655440002',
    'order_date',
    'date',
    '订单日期',
    '订单的创建日期',
    false,
    false,
    false,
    365,
    '["2024-01-01", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05"]'::jsonb
),
-- products表的列
(
    '770e8400-e29b-41d4-a716-446655440013',
    '660e8400-e29b-41d4-a716-446655440003',
    'product_id',
    'integer',
    '产品ID',
    '产品的唯一标识符',
    true,
    false,
    false,
    2340,
    '["101", "102", "103", "104", "105"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440014',
    '660e8400-e29b-41d4-a716-446655440003',
    'product_name',
    'varchar(255)',
    '产品名称',
    '产品的名称',
    false,
    false,
    false,
    2340,
    '["iPhone 15", "MacBook Pro", "iPad Air", "Apple Watch", "AirPods"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440015',
    '660e8400-e29b-41d4-a716-446655440003',
    'category',
    'varchar(100)',
    '产品类别',
    '产品所属的类别',
    false,
    false,
    false,
    25,
    '["手机", "电脑", "平板", "手表", "耳机"]'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440016',
    '660e8400-e29b-41d4-a716-446655440003',
    'price',
    'decimal(10,2)',
    '价格',
    '产品的价格',
    false,
    false,
    false,
    1500,
    '["999.00", "1999.00", "2999.00", "3999.00", "4999.00"]'::jsonb
);

-- =====================================================
-- 4. 示例关联关系
-- =====================================================
INSERT INTO data_relationships (id, from_column_id, to_column_id, relationship_type, is_manual, discovery_method, confidence, confidence_score, evidence, metadata) VALUES
-- customers.customer_id -> orders.customer_id (一对多)
(
    '880e8400-e29b-41d4-a716-446655440001',
    '770e8400-e29b-41d4-a716-446655440001', -- customers.customer_id
    '770e8400-e29b-41d4-a716-446655440007', -- orders.customer_id
    'one_to_many',
    false,
    'foreign_key',
    'high',
    0.95,
    ARRAY['外键约束', '命名约定匹配', '数据类型一致'],
    '{"relationship_strength": "strong", "data_consistency": 0.98}'::jsonb
),
-- products.product_id -> orders.product_id (一对多)
(
    '880e8400-e29b-41d4-a716-446655440002',
    '770e8400-e29b-41d4-a716-446655440013', -- products.product_id
    '770e8400-e29b-41d4-a716-446655440008', -- orders.product_id
    'one_to_many',
    false,
    'foreign_key',
    'high',
    0.93,
    ARRAY['外键约束', '命名约定匹配', '数据类型一致'],
    '{"relationship_strength": "strong", "data_consistency": 0.96}'::jsonb
);

-- =====================================================
-- 5. 示例对话历史
-- =====================================================
INSERT INTO chat_history (id, session_id, user_query, generated_sql, query_result_data, natural_language_response, visualization_spec, execution_time_ms, result_row_count, query_status, model_used, prompt_tokens, completion_tokens, total_tokens, user_rating) VALUES
(
    '990e8400-e29b-41d4-a716-446655440001',
    'session_001',
    '查询最近一个月的销售总额',
    'SELECT SUM(total_amount) as total_sales FROM orders WHERE order_date >= CURRENT_DATE - INTERVAL ''1 month''',
    '{"columns": ["total_sales"], "rows": [["1234567.89"]]}'::jsonb,
    '最近一个月的销售总额为 1,234,567.89 元。',
    '{"type": "metric", "title": "月度销售总额", "value": 1234567.89, "format": "currency"}'::jsonb,
    245,
    1,
    'completed',
    'gpt-3.5-turbo',
    150,
    80,
    230,
    5
),
(
    '990e8400-e29b-41d4-a716-446655440002',
    'session_001',
    '哪个客户的订单最多？',
    'SELECT c.customer_name, COUNT(o.order_id) as order_count FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY order_count DESC LIMIT 1',
    '{"columns": ["customer_name", "order_count"], "rows": [["张三", "25"]]}'::jsonb,
    '订单最多的客户是张三，共有 25 个订单。',
    '{"type": "bar_chart", "title": "客户订单数量", "data": [{"customer_name": "张三", "order_count": 25}], "x_field": "customer_name", "y_field": "order_count"}'::jsonb,
    189,
    1,
    'completed',
    'gpt-3.5-turbo',
    180,
    95,
    275,
    4
),
(
    '990e8400-e29b-41d4-a716-446655440003',
    'session_002',
    '显示各产品类别的销售情况',
    'SELECT p.category, COUNT(o.order_id) as order_count, SUM(o.total_amount) as total_sales FROM products p JOIN orders o ON p.product_id = o.product_id GROUP BY p.category ORDER BY total_sales DESC',
    '{"columns": ["category", "order_count", "total_sales"], "rows": [["手机", "1500", "2500000.00"], ["电脑", "800", "1800000.00"], ["平板", "600", "900000.00"]]}'::jsonb,
    '各产品类别的销售情况如下：手机类别销售最好，共1500个订单，总销售额250万元；电脑类别次之，800个订单，总销售额180万元；平板类别600个订单，总销售额90万元。',
    '{"type": "bar_chart", "title": "各产品类别销售情况", "data": [{"category": "手机", "order_count": 1500, "total_sales": 2500000}, {"category": "电脑", "order_count": 800, "total_sales": 1800000}, {"category": "平板", "order_count": 600, "total_sales": 900000}], "x_field": "category", "y_field": "total_sales"}'::jsonb,
    312,
    3,
    'completed',
    'gpt-3.5-turbo',
    220,
    120,
    340,
    5
);

-- =====================================================
-- 6. 示例数据质量检查记录
-- =====================================================
INSERT INTO data_quality_checks (id, data_source_id, data_table_id, data_column_id, check_type, check_name, check_description, check_rule, result_status, result_value, threshold_value, result_details, execution_time_ms) VALUES
(
    'aa0e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    '660e8400-e29b-41d4-a716-446655440001',
    '770e8400-e29b-41d4-a716-446655440002',
    'completeness',
    '客户姓名完整性检查',
    '检查客户姓名字段的完整性',
    '{"rule": "NOT NULL", "threshold": 0.95}'::jsonb,
    'passed',
    0.998,
    0.95,
    '{"total_rows": 15420, "null_rows": 31, "completeness_rate": 0.998}'::jsonb,
    45
),
(
    'aa0e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    '660e8400-e29b-41d4-a716-446655440001',
    '770e8400-e29b-41d4-a716-446655440003',
    'uniqueness',
    '邮箱唯一性检查',
    '检查邮箱地址的唯一性',
    '{"rule": "UNIQUE", "threshold": 0.99}'::jsonb,
    'warning',
    0.987,
    0.99,
    '{"total_rows": 15420, "unique_rows": 15220, "uniqueness_rate": 0.987, "duplicate_count": 200}'::jsonb,
    78
),
(
    'aa0e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440001',
    '660e8400-e29b-41d4-a716-446655440002',
    '770e8400-e29b-41d4-a716-446655440011',
    'validity',
    '订单金额有效性检查',
    '检查订单金额是否为正数',
    '{"rule": "total_amount > 0", "threshold": 0.999}'::jsonb,
    'passed',
    1.0,
    0.999,
    '{"total_rows": 89650, "valid_rows": 89650, "validity_rate": 1.0}'::jsonb,
    156
);

-- =====================================================
-- 7. 示例用户会话
-- =====================================================
INSERT INTO user_sessions (id, session_id, user_id, ip_address, user_agent, session_data, is_active, last_activity_at, expires_at) VALUES
(
    'bb0e8400-e29b-41d4-a716-446655440001',
    'session_001',
    'user_001',
    '*************'::inet,
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    '{"preferences": {"theme": "light", "language": "zh-CN"}, "last_queries": ["查询最近一个月的销售总额", "哪个客户的订单最多？"]}'::jsonb,
    true,
    NOW() - INTERVAL '5 minutes',
    NOW() + INTERVAL '8 hours'
),
(
    'bb0e8400-e29b-41d4-a716-446655440002',
    'session_002',
    'user_002',
    '*************'::inet,
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    '{"preferences": {"theme": "dark", "language": "zh-CN"}, "last_queries": ["显示各产品类别的销售情况"]}'::jsonb,
    true,
    NOW() - INTERVAL '15 minutes',
    NOW() + INTERVAL '8 hours'
);

-- =====================================================
-- 8. 更新统计信息
-- =====================================================
ANALYZE data_sources;
ANALYZE data_tables;
ANALYZE data_columns;
ANALYZE data_relationships;
ANALYZE chat_history;
ANALYZE data_quality_checks;
ANALYZE user_sessions;

-- =====================================================
-- 9. 验证种子数据
-- =====================================================
DO $$
DECLARE
    source_count INTEGER;
    table_count INTEGER;
    column_count INTEGER;
    relationship_count INTEGER;
    chat_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO source_count FROM data_sources;
    SELECT COUNT(*) INTO table_count FROM data_tables;
    SELECT COUNT(*) INTO column_count FROM data_columns;
    SELECT COUNT(*) INTO relationship_count FROM data_relationships;
    SELECT COUNT(*) INTO chat_count FROM chat_history;

    RAISE NOTICE '种子数据插入完成:';
    RAISE NOTICE '- 数据源: % 个', source_count;
    RAISE NOTICE '- 数据表: % 个', table_count;
    RAISE NOTICE '- 数据列: % 个', column_count;
    RAISE NOTICE '- 关联关系: % 个', relationship_count;
    RAISE NOTICE '- 对话记录: % 个', chat_count;
END $$;

-- 显示数据源概览
SELECT * FROM v_data_source_overview;

\echo '=========================================='
\echo 'AI-BI 种子数据插入完成！'
\echo '版本: 1.0'
\echo '创建时间: 2024-12-01'
\echo '=========================================='
