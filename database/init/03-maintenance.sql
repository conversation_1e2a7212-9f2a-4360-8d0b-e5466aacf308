-- =====================================================
-- AI-BI 系统数据库维护脚本
-- 版本: 1.0
-- 创建时间: 2024-12-01
-- 描述: 数据库维护、清理和优化脚本
-- =====================================================

-- 设置时区
SET timezone = 'UTC';

-- =====================================================
-- 1. 定期清理函数
-- =====================================================

-- 清理过期的用户会话
CREATE OR REPLACE FUNCTION cleanup_expired_sessions_job()
RETURNS TABLE(
    deleted_sessions INTEGER,
    cleanup_time TIMESTAMPTZ
) AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除过期的会话
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() 
       OR (last_activity_at < NOW() - INTERVAL '7 days' AND is_active = false);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'CLEANUP_SESSIONS', '清理过期会话', 
     json_build_object('deleted_count', deleted_count, 'cleanup_time', NOW()));
    
    RETURN QUERY SELECT deleted_count, NOW();
END;
$$ LANGUAGE plpgsql;

-- 清理旧的系统日志
CREATE OR REPLACE FUNCTION cleanup_old_logs_job(retention_days INTEGER DEFAULT 30)
RETURNS TABLE(
    deleted_logs INTEGER,
    cleanup_time TIMESTAMPTZ
) AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除超过保留期的日志（保留ERROR和FATAL级别的日志更长时间）
    DELETE FROM system_logs 
    WHERE created_at < NOW() - (retention_days || ' days')::INTERVAL
      AND log_level NOT IN ('ERROR', 'FATAL');
    
    -- 删除超过90天的ERROR和FATAL日志
    DELETE FROM system_logs 
    WHERE created_at < NOW() - INTERVAL '90 days'
      AND log_level IN ('ERROR', 'FATAL');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'CLEANUP_LOGS', '清理旧日志', 
     json_build_object('deleted_count', deleted_count, 'retention_days', retention_days, 'cleanup_time', NOW()));
    
    RETURN QUERY SELECT deleted_count, NOW();
END;
$$ LANGUAGE plpgsql;

-- 清理旧的对话历史（保留最近3个月的数据）
CREATE OR REPLACE FUNCTION cleanup_old_chat_history(retention_days INTEGER DEFAULT 90)
RETURNS TABLE(
    deleted_chats INTEGER,
    cleanup_time TIMESTAMPTZ
) AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除超过保留期的对话历史
    DELETE FROM chat_history 
    WHERE created_at < NOW() - (retention_days || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'CLEANUP_CHAT_HISTORY', '清理旧对话历史', 
     json_build_object('deleted_count', deleted_count, 'retention_days', retention_days, 'cleanup_time', NOW()));
    
    RETURN QUERY SELECT deleted_count, NOW();
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. 数据库优化函数
-- =====================================================

-- 更新表统计信息
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS TABLE(
    table_name TEXT,
    analyze_time TIMESTAMPTZ
) AS $$
DECLARE
    tbl_name TEXT;
    tables_to_analyze TEXT[] := ARRAY[
        'data_sources', 'data_tables', 'data_columns', 'data_relationships',
        'chat_history', 'system_config', 'query_templates', 'data_quality_checks',
        'user_sessions', 'system_logs'
    ];
BEGIN
    FOREACH tbl_name IN ARRAY tables_to_analyze
    LOOP
        EXECUTE 'ANALYZE ' || tbl_name;
        RETURN QUERY SELECT tbl_name, NOW();
    END LOOP;
    
    -- 记录统计更新日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'UPDATE_STATISTICS', '更新表统计信息', 
     json_build_object('tables_count', array_length(tables_to_analyze, 1), 'update_time', NOW()));
END;
$$ LANGUAGE plpgsql;

-- 重建索引（如果需要）
CREATE OR REPLACE FUNCTION rebuild_indexes_if_needed()
RETURNS TABLE(
    index_name TEXT,
    rebuild_status TEXT
) AS $$
DECLARE
    idx_record RECORD;
    rebuild_count INTEGER := 0;
BEGIN
    -- 查找可能需要重建的索引（这里简化处理，实际应该根据索引膨胀率判断）
    FOR idx_record IN 
        SELECT schemaname, tablename, indexname 
        FROM pg_indexes 
        WHERE schemaname = 'public' 
          AND indexname LIKE 'idx_%'
    LOOP
        -- 这里可以添加更复杂的索引健康检查逻辑
        -- 目前只是示例，不实际重建索引
        RETURN QUERY SELECT idx_record.indexname, 'checked'::TEXT;
    END LOOP;
    
    -- 记录索引检查日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'CHECK_INDEXES', '检查索引状态', 
     json_build_object('indexes_checked', rebuild_count, 'check_time', NOW()));
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. 数据完整性检查函数
-- =====================================================

-- 检查数据完整性
CREATE OR REPLACE FUNCTION check_data_integrity()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    details JSONB
) AS $$
DECLARE
    orphaned_tables INTEGER;
    orphaned_columns INTEGER;
    orphaned_relationships INTEGER;
    invalid_configs INTEGER;
BEGIN
    -- 检查孤立的数据表
    SELECT COUNT(*) INTO orphaned_tables
    FROM data_tables dt
    LEFT JOIN data_sources ds ON dt.data_source_id = ds.id
    WHERE ds.id IS NULL;
    
    -- 检查孤立的数据列
    SELECT COUNT(*) INTO orphaned_columns
    FROM data_columns dc
    LEFT JOIN data_tables dt ON dc.data_table_id = dt.id
    WHERE dt.id IS NULL;
    
    -- 检查无效的关联关系
    SELECT COUNT(*) INTO orphaned_relationships
    FROM data_relationships dr
    LEFT JOIN data_columns fc ON dr.from_column_id = fc.id
    LEFT JOIN data_columns tc ON dr.to_column_id = tc.id
    WHERE fc.id IS NULL OR tc.id IS NULL;
    
    -- 检查无效的连接配置
    SELECT COUNT(*) INTO invalid_configs
    FROM data_sources
    WHERE NOT validate_connection_config(connection_config);
    
    -- 返回检查结果
    RETURN QUERY VALUES
        ('orphaned_tables', CASE WHEN orphaned_tables = 0 THEN 'OK' ELSE 'WARNING' END, json_build_object('count', orphaned_tables)),
        ('orphaned_columns', CASE WHEN orphaned_columns = 0 THEN 'OK' ELSE 'WARNING' END, json_build_object('count', orphaned_columns)),
        ('orphaned_relationships', CASE WHEN orphaned_relationships = 0 THEN 'OK' ELSE 'WARNING' END, json_build_object('count', orphaned_relationships)),
        ('invalid_configs', CASE WHEN invalid_configs = 0 THEN 'OK' ELSE 'ERROR' END, json_build_object('count', invalid_configs));
    
    -- 记录完整性检查日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'INTEGRITY_CHECK', '数据完整性检查', 
     json_build_object(
         'orphaned_tables', orphaned_tables,
         'orphaned_columns', orphaned_columns,
         'orphaned_relationships', orphaned_relationships,
         'invalid_configs', invalid_configs,
         'check_time', NOW()
     ));
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. 系统健康检查函数
-- =====================================================

-- 系统健康检查
CREATE OR REPLACE FUNCTION system_health_check()
RETURNS TABLE(
    metric_name TEXT,
    metric_value NUMERIC,
    status TEXT,
    threshold_value NUMERIC
) AS $$
DECLARE
    total_data_sources INTEGER;
    active_data_sources INTEGER;
    total_tables INTEGER;
    synced_tables INTEGER;
    total_relationships INTEGER;
    high_confidence_relationships INTEGER;
    avg_query_time NUMERIC;
    failed_queries INTEGER;
    total_queries INTEGER;
BEGIN
    -- 数据源健康度
    SELECT COUNT(*) INTO total_data_sources FROM data_sources;
    SELECT COUNT(*) INTO active_data_sources FROM data_sources WHERE status = 'active';
    
    -- 数据同步健康度
    SELECT COUNT(*) INTO total_tables FROM data_tables;
    SELECT COUNT(*) INTO synced_tables FROM data_tables WHERE is_synced = true;
    
    -- 关联关系质量
    SELECT COUNT(*) INTO total_relationships FROM data_relationships;
    SELECT COUNT(*) INTO high_confidence_relationships FROM data_relationships WHERE confidence = 'high';
    
    -- 查询性能
    SELECT AVG(execution_time_ms) INTO avg_query_time FROM chat_history WHERE created_at > NOW() - INTERVAL '24 hours';
    SELECT COUNT(*) INTO failed_queries FROM chat_history WHERE query_status = 'failed' AND created_at > NOW() - INTERVAL '24 hours';
    SELECT COUNT(*) INTO total_queries FROM chat_history WHERE created_at > NOW() - INTERVAL '24 hours';
    
    -- 返回健康指标
    RETURN QUERY VALUES
        ('data_source_availability', 
         CASE WHEN total_data_sources > 0 THEN (active_data_sources::NUMERIC / total_data_sources) * 100 ELSE 0 END,
         CASE WHEN total_data_sources = 0 OR (active_data_sources::NUMERIC / total_data_sources) >= 0.8 THEN 'GOOD' 
              WHEN (active_data_sources::NUMERIC / total_data_sources) >= 0.5 THEN 'WARNING' 
              ELSE 'CRITICAL' END,
         80),
        ('data_sync_rate',
         CASE WHEN total_tables > 0 THEN (synced_tables::NUMERIC / total_tables) * 100 ELSE 0 END,
         CASE WHEN total_tables = 0 OR (synced_tables::NUMERIC / total_tables) >= 0.9 THEN 'GOOD'
              WHEN (synced_tables::NUMERIC / total_tables) >= 0.7 THEN 'WARNING'
              ELSE 'CRITICAL' END,
         90),
        ('relationship_quality',
         CASE WHEN total_relationships > 0 THEN (high_confidence_relationships::NUMERIC / total_relationships) * 100 ELSE 0 END,
         CASE WHEN total_relationships = 0 OR (high_confidence_relationships::NUMERIC / total_relationships) >= 0.7 THEN 'GOOD'
              WHEN (high_confidence_relationships::NUMERIC / total_relationships) >= 0.5 THEN 'WARNING'
              ELSE 'CRITICAL' END,
         70),
        ('avg_query_performance',
         COALESCE(avg_query_time, 0),
         CASE WHEN avg_query_time IS NULL OR avg_query_time <= 1000 THEN 'GOOD'
              WHEN avg_query_time <= 3000 THEN 'WARNING'
              ELSE 'CRITICAL' END,
         1000),
        ('query_success_rate',
         CASE WHEN total_queries > 0 THEN ((total_queries - failed_queries)::NUMERIC / total_queries) * 100 ELSE 100 END,
         CASE WHEN total_queries = 0 OR ((total_queries - failed_queries)::NUMERIC / total_queries) >= 0.95 THEN 'GOOD'
              WHEN ((total_queries - failed_queries)::NUMERIC / total_queries) >= 0.85 THEN 'WARNING'
              ELSE 'CRITICAL' END,
         95);
    
    -- 记录健康检查日志
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'HEALTH_CHECK', '系统健康检查', 
     json_build_object(
         'total_data_sources', total_data_sources,
         'active_data_sources', active_data_sources,
         'total_tables', total_tables,
         'synced_tables', synced_tables,
         'avg_query_time', avg_query_time,
         'check_time', NOW()
     ));
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. 自动维护任务调度（示例）
-- =====================================================

-- 创建维护任务执行函数
CREATE OR REPLACE FUNCTION run_daily_maintenance()
RETURNS TABLE(
    task_name TEXT,
    execution_status TEXT,
    execution_time TIMESTAMPTZ,
    details JSONB
) AS $$
DECLARE
    session_cleanup_result RECORD;
    log_cleanup_result RECORD;
    stats_update_result RECORD;
    integrity_check_result RECORD;
    health_check_result RECORD;
BEGIN
    -- 执行会话清理
    SELECT * INTO session_cleanup_result FROM cleanup_expired_sessions_job();
    RETURN QUERY SELECT 'session_cleanup'::TEXT, 'completed'::TEXT, session_cleanup_result.cleanup_time,
                        json_build_object('deleted_sessions', session_cleanup_result.deleted_sessions);

    -- 执行日志清理
    SELECT * INTO log_cleanup_result FROM cleanup_old_logs_job(30);
    RETURN QUERY SELECT 'log_cleanup'::TEXT, 'completed'::TEXT, log_cleanup_result.cleanup_time,
                        json_build_object('deleted_logs', log_cleanup_result.deleted_logs);

    -- 更新统计信息
    PERFORM update_table_statistics();
    RETURN QUERY SELECT 'statistics_update'::TEXT, 'completed'::TEXT, NOW(),
                        json_build_object('status', 'success');

    -- 记录维护任务完成
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'DAILY_MAINTENANCE', '每日维护任务完成',
     json_build_object('execution_time', NOW()));
END;
$$ LANGUAGE plpgsql;

-- 创建周度维护任务
CREATE OR REPLACE FUNCTION run_weekly_maintenance()
RETURNS TABLE(
    task_name TEXT,
    execution_status TEXT,
    execution_time TIMESTAMPTZ,
    details JSONB
) AS $$
BEGIN
    -- 执行数据完整性检查
    RETURN QUERY
    SELECT 'integrity_check'::TEXT, 'completed'::TEXT, NOW(),
           json_build_object('results', (SELECT json_agg(row_to_json(t)) FROM check_data_integrity() t));

    -- 执行系统健康检查
    RETURN QUERY
    SELECT 'health_check'::TEXT, 'completed'::TEXT, NOW(),
           json_build_object('results', (SELECT json_agg(row_to_json(t)) FROM system_health_check() t));

    -- 清理旧的对话历史
    RETURN QUERY
    SELECT 'chat_history_cleanup'::TEXT, 'completed'::TEXT, NOW(),
           json_build_object('results', (SELECT row_to_json(t) FROM cleanup_old_chat_history(90) t));

    -- 记录周度维护任务完成
    INSERT INTO system_logs (log_level, component, action, message, details) VALUES
    ('INFO', 'MAINTENANCE', 'WEEKLY_MAINTENANCE', '周度维护任务完成',
     json_build_object('execution_time', NOW()));
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. 数据备份和恢复辅助函数
-- =====================================================

-- 获取数据库大小信息
CREATE OR REPLACE FUNCTION get_database_size_info()
RETURNS TABLE(
    database_name TEXT,
    size_bytes BIGINT,
    size_pretty TEXT,
    table_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        current_database()::TEXT,
        pg_database_size(current_database()),
        pg_size_pretty(pg_database_size(current_database())),
        (SELECT COUNT(*)::INTEGER FROM information_schema.tables WHERE table_schema = 'public');
END;
$$ LANGUAGE plpgsql;

-- 获取表大小信息
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
    table_name TEXT,
    row_count BIGINT,
    size_bytes BIGINT,
    size_pretty TEXT,
    index_size_bytes BIGINT,
    index_size_pretty TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.table_name::TEXT,
        (SELECT n_tup_ins - n_tup_del FROM pg_stat_user_tables WHERE relname = t.table_name),
        pg_total_relation_size(t.table_name::regclass),
        pg_size_pretty(pg_total_relation_size(t.table_name::regclass)),
        pg_indexes_size(t.table_name::regclass),
        pg_size_pretty(pg_indexes_size(t.table_name::regclass))
    FROM information_schema.tables t
    WHERE t.table_schema = 'public'
      AND t.table_type = 'BASE TABLE'
    ORDER BY pg_total_relation_size(t.table_name::regclass) DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. 监控和报告函数
-- =====================================================

-- 生成系统状态报告
CREATE OR REPLACE FUNCTION generate_system_report()
RETURNS TABLE(
    report_section TEXT,
    report_data JSONB
) AS $$
BEGIN
    -- 数据源状态
    RETURN QUERY
    SELECT 'data_sources'::TEXT,
           (SELECT json_agg(row_to_json(t)) FROM v_data_source_overview t)::JSONB;

    -- 系统健康状态
    RETURN QUERY
    SELECT 'health_metrics'::TEXT,
           (SELECT json_agg(row_to_json(t)) FROM system_health_check() t)::JSONB;

    -- 数据库大小信息
    RETURN QUERY
    SELECT 'database_size'::TEXT,
           (SELECT row_to_json(t) FROM get_database_size_info() t)::JSONB;

    -- 最近的错误日志
    RETURN QUERY
    SELECT 'recent_errors'::TEXT,
           (SELECT json_agg(row_to_json(t)) FROM (
               SELECT log_level, component, action, message, created_at
               FROM system_logs
               WHERE log_level IN ('ERROR', 'FATAL')
                 AND created_at > NOW() - INTERVAL '24 hours'
               ORDER BY created_at DESC
               LIMIT 10
           ) t)::JSONB;

    -- 查询性能统计
    RETURN QUERY
    SELECT 'query_performance'::TEXT,
           (SELECT json_agg(row_to_json(t)) FROM (
               SELECT
                   DATE(created_at) as query_date,
                   COUNT(*) as total_queries,
                   AVG(execution_time_ms) as avg_execution_time,
                   COUNT(CASE WHEN query_status = 'failed' THEN 1 END) as failed_queries
               FROM chat_history
               WHERE created_at > NOW() - INTERVAL '7 days'
               GROUP BY DATE(created_at)
               ORDER BY query_date DESC
           ) t)::JSONB;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 8. 初始化维护任务
-- =====================================================

-- 插入维护配置
INSERT INTO system_config (config_key, config_value, description, is_encrypted) VALUES
('maintenance_schedule',
 '{"daily_maintenance": {"enabled": true, "time": "02:00"}, "weekly_maintenance": {"enabled": true, "day": "sunday", "time": "03:00"}, "log_retention_days": 30, "chat_history_retention_days": 90}'::jsonb,
 '维护任务调度配置',
 false),
('backup_config',
 '{"enabled": true, "schedule": "daily", "retention_days": 7, "compression": true}'::jsonb,
 '备份配置',
 false)
ON CONFLICT (config_key) DO UPDATE SET
    config_value = EXCLUDED.config_value,
    updated_at = NOW();

-- 记录维护脚本初始化完成
INSERT INTO system_logs (log_level, component, action, message, details) VALUES
('INFO', 'MAINTENANCE', 'SCRIPT_INITIALIZATION', '维护脚本初始化完成',
 json_build_object('script_version', '1.0', 'initialization_time', NOW()));

-- =====================================================
-- 脚本执行完成
-- =====================================================
\echo '=========================================='
\echo 'AI-BI 数据库维护脚本加载完成！'
\echo '版本: 1.0'
\echo '创建时间: 2024-12-01'
\echo ''
\echo '可用的维护函数:'
\echo '- cleanup_expired_sessions_job(): 清理过期会话'
\echo '- cleanup_old_logs_job(): 清理旧日志'
\echo '- cleanup_old_chat_history(): 清理旧对话历史'
\echo '- update_table_statistics(): 更新表统计信息'
\echo '- check_data_integrity(): 检查数据完整性'
\echo '- system_health_check(): 系统健康检查'
\echo '- run_daily_maintenance(): 执行每日维护'
\echo '- run_weekly_maintenance(): 执行周度维护'
\echo '- generate_system_report(): 生成系统报告'
\echo '=========================================='
