-- =====================================================
-- AI-BI 数据库设置验证脚本
-- 版本: 1.0
-- 创建时间: 2024-12-01
-- 描述: 验证数据库初始化是否成功完成
-- =====================================================

\echo '开始验证 AI-BI 数据库设置...'
\echo ''

-- =====================================================
-- 1. 检查数据库基本信息
-- =====================================================
\echo '1. 数据库基本信息:'
SELECT 
    current_database() as database_name,
    current_user as current_user,
    version() as postgresql_version;

\echo ''

-- =====================================================
-- 2. 检查扩展是否安装
-- =====================================================
\echo '2. 已安装的扩展:'
SELECT 
    extname as extension_name,
    extversion as version
FROM pg_extension 
WHERE extname IN ('uuid-ossp', 'pgcrypto', 'btree_gin', 'pg_trgm')
ORDER BY extname;

\echo ''

-- =====================================================
-- 3. 检查表是否创建成功
-- =====================================================
\echo '3. 数据库表:'
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

\echo ''

-- =====================================================
-- 4. 检查视图是否创建成功
-- =====================================================
\echo '4. 数据库视图:'
SELECT 
    table_name as view_name
FROM information_schema.views 
WHERE table_schema = 'public'
ORDER BY table_name;

\echo ''

-- =====================================================
-- 5. 检查函数是否创建成功
-- =====================================================
\echo '5. 自定义函数:'
SELECT 
    routine_name as function_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
  AND routine_name NOT LIKE 'pg_%'
ORDER BY routine_name;

\echo ''

-- =====================================================
-- 6. 检查触发器是否创建成功
-- =====================================================
\echo '6. 触发器:'
SELECT 
    trigger_name,
    event_object_table as table_name,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

\echo ''

-- =====================================================
-- 7. 检查索引是否创建成功
-- =====================================================
\echo '7. 索引统计:'
SELECT 
    schemaname,
    COUNT(*) as index_count
FROM pg_indexes 
WHERE schemaname = 'public'
GROUP BY schemaname;

\echo ''

-- =====================================================
-- 8. 检查数据是否插入成功
-- =====================================================
\echo '8. 数据统计:'
SELECT 
    'data_sources' as table_name,
    COUNT(*) as row_count
FROM data_sources
UNION ALL
SELECT 
    'data_tables' as table_name,
    COUNT(*) as row_count
FROM data_tables
UNION ALL
SELECT 
    'data_columns' as table_name,
    COUNT(*) as row_count
FROM data_columns
UNION ALL
SELECT 
    'data_relationships' as table_name,
    COUNT(*) as row_count
FROM data_relationships
UNION ALL
SELECT 
    'chat_history' as table_name,
    COUNT(*) as row_count
FROM chat_history
UNION ALL
SELECT 
    'system_config' as table_name,
    COUNT(*) as row_count
FROM system_config
UNION ALL
SELECT 
    'query_templates' as table_name,
    COUNT(*) as row_count
FROM query_templates
UNION ALL
SELECT 
    'data_quality_checks' as table_name,
    COUNT(*) as row_count
FROM data_quality_checks
UNION ALL
SELECT 
    'user_sessions' as table_name,
    COUNT(*) as row_count
FROM user_sessions
UNION ALL
SELECT 
    'system_logs' as table_name,
    COUNT(*) as row_count
FROM system_logs
ORDER BY table_name;

\echo ''

-- =====================================================
-- 9. 检查外键约束
-- =====================================================
\echo '9. 外键约束:'
SELECT 
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

\echo ''

-- =====================================================
-- 10. 测试核心功能
-- =====================================================
\echo '10. 功能测试:'

-- 测试数据源概览视图
\echo '数据源概览:'
SELECT 
    name,
    type,
    status,
    table_count,
    column_count
FROM v_data_source_overview
ORDER BY name;

\echo ''

-- 测试系统健康检查
\echo '系统健康检查:'
SELECT 
    metric_name,
    metric_value,
    status
FROM system_health_check()
ORDER BY metric_name;

\echo ''

-- 测试数据完整性检查
\echo '数据完整性检查:'
SELECT 
    check_name,
    status,
    details
FROM check_data_integrity()
ORDER BY check_name;

\echo ''

-- =====================================================
-- 11. 数据库大小信息
-- =====================================================
\echo '11. 数据库大小信息:'
SELECT * FROM get_database_size_info();

\echo ''

-- =====================================================
-- 12. 验证结果总结
-- =====================================================
\echo '12. 验证结果总结:'

DO $$
DECLARE
    table_count INTEGER;
    view_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    data_source_count INTEGER;
    config_count INTEGER;
    expected_tables INTEGER := 10;
    expected_views INTEGER := 3;
    min_functions INTEGER := 10;
    min_configs INTEGER := 5;
BEGIN
    -- 统计各种对象数量
    SELECT COUNT(*) INTO table_count FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    SELECT COUNT(*) INTO view_count FROM information_schema.views WHERE table_schema = 'public';
    SELECT COUNT(*) INTO function_count FROM information_schema.routines WHERE routine_schema = 'public' AND routine_name NOT LIKE 'pg_%';
    SELECT COUNT(*) INTO trigger_count FROM information_schema.triggers WHERE trigger_schema = 'public';
    SELECT COUNT(*) INTO data_source_count FROM data_sources;
    SELECT COUNT(*) INTO config_count FROM system_config;
    
    -- 输出验证结果
    RAISE NOTICE '========================================';
    RAISE NOTICE 'AI-BI 数据库验证结果:';
    RAISE NOTICE '========================================';
    RAISE NOTICE '表数量: % (期望: %)', table_count, expected_tables;
    RAISE NOTICE '视图数量: % (期望: %)', view_count, expected_views;
    RAISE NOTICE '函数数量: % (最少: %)', function_count, min_functions;
    RAISE NOTICE '触发器数量: %', trigger_count;
    RAISE NOTICE '示例数据源: %', data_source_count;
    RAISE NOTICE '系统配置: % (最少: %)', config_count, min_configs;
    RAISE NOTICE '========================================';
    
    -- 验证是否满足最低要求
    IF table_count >= expected_tables AND 
       view_count >= expected_views AND 
       function_count >= min_functions AND 
       config_count >= min_configs THEN
        RAISE NOTICE '✅ 数据库设置验证通过！';
    ELSE
        RAISE NOTICE '❌ 数据库设置验证失败，请检查初始化脚本！';
    END IF;
    
    RAISE NOTICE '========================================';
END $$;

\echo ''
\echo '验证完成！'
