# API 文档

AI-BI 系统 REST API 接口文档。

## 基础信息

- **Base URL**: `http://localhost:3001`
- **Content-Type**: `application/json`
- **认证**: 暂未实现，后续版本将支持 JWT 认证

## 通用响应格式

所有 API 响应都遵循统一的格式：

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "error": null
}
```

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "message": "操作失败"
}
```

## 数据源管理 API

### 获取数据源列表

**GET** `/api/data-sources`

获取所有数据源的列表。

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-1",
      "name": "生产数据库",
      "type": "postgresql",
      "status": "connected",
      "createdAt": "2023-12-01T10:00:00Z",
      "updatedAt": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### 创建数据源

**POST** `/api/data-sources`

创建新的数据源。

**请求体:**
```json
{
  "name": "测试数据库",
  "type": "postgresql",
  "connectionConfig": {
    "host": "localhost",
    "port": 5432,
    "database": "test_db",
    "username": "test_user",
    "password": "test_password",
    "ssl": false
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-new"
  },
  "message": "数据源创建成功"
}
```

### 获取数据源详情

**GET** `/api/data-sources/{id}`

获取指定数据源的详细信息。

**路径参数:**
- `id` (string): 数据源ID

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-1",
    "name": "生产数据库",
    "type": "postgresql",
    "tables": [
      {
        "id": "table-uuid-1",
        "originalName": "users",
        "aliasName": "用户表",
        "description": "系统用户信息表",
        "columns": [
          {
            "id": "column-uuid-1",
            "originalName": "id",
            "aliasName": "用户ID",
            "dataType": "integer",
            "description": "用户唯一标识",
            "isPrimaryKey": true
          }
        ]
      }
    ],
    "relationships": [
      {
        "id": "rel-uuid-1",
        "fromColumnId": "column-uuid-1",
        "toColumnId": "column-uuid-2",
        "type": "one_to_many",
        "isManual": false
      }
    ]
  }
}
```

### 更新数据源

**PUT** `/api/data-sources/{id}`

更新指定数据源的信息。

**路径参数:**
- `id` (string): 数据源ID

**请求体:**
```json
{
  "name": "更新后的数据源名称",
  "connectionConfig": {
    "host": "new-host",
    "port": 5432,
    "database": "new_db",
    "username": "new_user",
    "password": "new_password"
  }
}
```

### 删除数据源

**DELETE** `/api/data-sources/{id}`

删除指定的数据源。

**路径参数:**
- `id` (string): 数据源ID

### 测试连接

**POST** `/api/data-sources/test-connection`

测试数据源连接是否正常。

**请求体:**
```json
{
  "type": "postgresql",
  "connectionConfig": {
    "host": "localhost",
    "port": 5432,
    "database": "test_db",
    "username": "test_user",
    "password": "test_password"
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "connected": true
  },
  "message": "连接测试成功"
}
```

### 同步元数据

**POST** `/api/data-sources/{id}/sync`

同步指定数据源的元数据信息。

**路径参数:**
- `id` (string): 数据源ID

### 检查连接状态

**GET** `/api/data-sources/{id}/status`

检查指定数据源的连接状态。

**路径参数:**
- `id` (string): 数据源ID

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "connected"
  }
}
```

## 语义层管理 API

### 获取语义上下文

**GET** `/api/semantic/context`

获取完整的语义层上下文信息，用于AI模型理解数据结构。

**响应示例:**
```json
{
  "success": true,
  "data": {
    "context": "# 数据模型语义层\n\n## 数据源: 生产数据库\n### 表: 用户表\n描述: 系统用户信息表\n字段:\n- 用户ID (integer) [主键] - 用户唯一标识\n- 用户姓名 (varchar) - 用户的姓名\n\n## 表关联关系\n- 用户表.用户ID -> 订单表.客户ID (one_to_many)"
  }
}
```

### 更新表别名

**PUT** `/api/semantic/tables/{tableId}/alias`

更新数据表的业务别名和描述。

**路径参数:**
- `tableId` (string): 数据表ID

**请求体:**
```json
{
  "aliasName": "客户信息表",
  "description": "存储客户基本信息和联系方式"
}
```

### 更新列别名

**PUT** `/api/semantic/columns/{columnId}/alias`

更新数据列的业务别名和描述。

**路径参数:**
- `columnId` (string): 数据列ID

**请求体:**
```json
{
  "aliasName": "客户姓名",
  "description": "客户的真实姓名"
}
```

### 创建关联关系

**POST** `/api/semantic/relationships`

创建表之间的关联关系。

**请求体:**
```json
{
  "fromColumnId": "column-uuid-1",
  "toColumnId": "column-uuid-2",
  "relationshipType": "one_to_many"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "relationship-uuid-new"
  },
  "message": "关联关系创建成功"
}
```

### 更新关联关系

**PUT** `/api/semantic/relationships/{relationshipId}`

更新现有的关联关系。

**路径参数:**
- `relationshipId` (string): 关联关系ID

**请求体:**
```json
{
  "relationshipType": "many_to_one"
}
```

### 删除关联关系

**DELETE** `/api/semantic/relationships/{relationshipId}`

删除指定的关联关系。

**路径参数:**
- `relationshipId` (string): 关联关系ID

### 自动发现关联关系

**POST** `/api/semantic/data-sources/{dataSourceId}/discover-relationships`

自动发现指定数据源中的关联关系。

**路径参数:**
- `dataSourceId` (string): 数据源ID

**响应示例:**
```json
{
  "success": true,
  "data": {
    "discoveredCount": 5
  },
  "message": "成功发现 5 个关联关系"
}
```

## 聊天分析 API

### 发送聊天消息

**POST** `/api/chat`

发送自然语言查询请求。

**请求体:**
```json
{
  "sessionId": "session-uuid-123",
  "prompt": "最近一个月的销售总额是多少？"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": "chat-uuid-456",
    "sessionId": "session-uuid-123",
    "userPrompt": "最近一个月的销售总额是多少？",
    "responseText": "最近一个月的总销售额为 1,234,567 元。",
    "data": {
      "columns": ["总销售额"],
      "rows": [[1234567]]
    },
    "visualization": {
      "type": "bar_chart",
      "title": "销售总额",
      "spec": {
        "data": {
          "values": [{"总销售额": 1234567}]
        },
        "mark": "bar",
        "encoding": {
          "x": {"field": "总销售额", "type": "quantitative"}
        }
      }
    },
    "generatedSql": "SELECT SUM(amount) as total_amount FROM orders WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'"
  }
}
```

### 获取聊天历史

**GET** `/api/chat/history/{sessionId}`

获取指定会话的聊天历史记录。

**路径参数:**
- `sessionId` (string): 会话ID

**查询参数:**
- `limit` (number, 可选): 返回记录数量限制，默认50

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "chat-uuid-456",
      "sessionId": "session-uuid-123",
      "userPrompt": "最近一个月的销售总额是多少？",
      "responseText": "最近一个月的总销售额为 1,234,567 元。",
      "data": {
        "columns": ["总销售额"],
        "rows": [[1234567]]
      },
      "visualization": {
        "type": "bar_chart",
        "title": "销售总额"
      },
      "generatedSql": "SELECT SUM(amount) as total_amount FROM orders WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'"
    }
  ]
}
```

## 健康检查 API

### 系统健康检查

**GET** `/health`

检查系统运行状态。

**响应示例:**
```json
{
  "status": "ok",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "1.0.0"
}
```

## 错误代码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 502 | 外部服务错误 |

## 数据类型定义

### DataSourceType
```typescript
type DataSourceType = 'postgresql' | 'mysql' | 'csv' | 'excel';
```

### DataSourceStatus
```typescript
type DataSourceStatus = 'connected' | 'disconnected' | 'error' | 'syncing';
```

### RelationshipType
```typescript
type RelationshipType = 'one_to_one' | 'one_to_many' | 'many_to_one';
```

### VisualizationType
```typescript
type VisualizationType = 'bar_chart' | 'line_chart' | 'pie_chart' | 'table' | 'scatter_plot';
```

## 使用示例

### JavaScript/TypeScript

```typescript
// 创建数据源
const createDataSource = async () => {
  const response = await fetch('/api/data-sources', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: '测试数据库',
      type: 'postgresql',
      connectionConfig: {
        host: 'localhost',
        port: 5432,
        database: 'test_db',
        username: 'test_user',
        password: 'test_password'
      }
    })
  });
  
  const result = await response.json();
  console.log(result);
};

// 发送聊天消息
const sendChatMessage = async () => {
  const response = await fetch('/api/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      sessionId: 'my-session-123',
      prompt: '显示所有用户的订单统计'
    })
  });
  
  const result = await response.json();
  console.log(result);
};
```

### Python

```python
import requests

# 创建数据源
def create_data_source():
    url = 'http://localhost:3001/api/data-sources'
    data = {
        'name': '测试数据库',
        'type': 'postgresql',
        'connectionConfig': {
            'host': 'localhost',
            'port': 5432,
            'database': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
        }
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 发送聊天消息
def send_chat_message():
    url = 'http://localhost:3001/api/chat'
    data = {
        'sessionId': 'my-session-123',
        'prompt': '显示所有用户的订单统计'
    }
    
    response = requests.post(url, json=data)
    return response.json()
```

## 限制和注意事项

1. **请求大小限制**: 请求体最大 10MB
2. **并发限制**: 单个IP每分钟最多100个请求
3. **会话超时**: 聊天会话24小时后自动过期
4. **文件上传**: CSV/Excel文件最大支持100MB
5. **查询超时**: SQL查询最长执行时间30秒

## 版本历史

- **v1.0.0**: 初始版本，包含基础的数据源管理、语义层配置和聊天分析功能
