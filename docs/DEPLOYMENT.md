# 部署指南

本文档详细说明如何在不同环境中部署 AI-BI 智能商业分析系统。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │  数据库 (PostgreSQL) │
│   端口: 3000     │◄──►│   端口: 3001     │◄──►│   端口: 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Redis 缓存     │
                       │   端口: 6379     │
                       └─────────────────┘
```

## 🐳 Docker 部署 (推荐)

### 前置要求
- Docker 20.10+
- Docker Compose 2.0+

### 快速部署

1. **克隆项目**
```bash
git clone <repository-url>
cd ai-bi-system
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑配置文件
nano backend/.env
```

3. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

4. **初始化数据库**
```bash
# 运行数据库迁移
docker-compose exec backend npm run migrate

# 运行种子数据
docker-compose exec backend npm run db:seed
```

5. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:3001
- 数据库管理: http://localhost:3001/health

### Docker Compose 配置

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ai_bi_system
      POSTGRES_USER: ai_bi_user
      POSTGRES_PASSWORD: ai_bi_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=****************************************************/ai_bi_system
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - postgres
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
```

## 🖥️ 手动部署

### 系统要求
- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- PM2 (生产环境推荐)

### 1. 数据库设置

#### PostgreSQL 安装
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 创建数据库
```sql
-- 连接到 PostgreSQL
sudo -u postgres psql

-- 创建用户和数据库
CREATE USER ai_bi_user WITH PASSWORD 'your_password';
CREATE DATABASE ai_bi_system OWNER ai_bi_user;
GRANT ALL PRIVILEGES ON DATABASE ai_bi_system TO ai_bi_user;
```

#### Redis 安装
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

### 2. 后端部署

```bash
# 进入后端目录
cd backend

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env
nano .env

# 生成 Prisma 客户端
npm run db:generate

# 运行数据库迁移
npm run migrate:deploy

# 构建应用
npm run build

# 使用 PM2 启动 (推荐)
npm install -g pm2
pm2 start ecosystem.config.js

# 或直接启动
npm start
```

#### PM2 配置文件 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'ai-bi-backend',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### 3. 前端部署

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm ci

# 构建生产版本
npm run build

# 使用 serve 部署
npm install -g serve
serve -s dist -l 3000

# 或使用 Nginx 部署 (推荐)
sudo cp -r dist/* /var/www/html/
```

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## ☁️ 云平台部署

### AWS 部署

#### 使用 ECS + RDS
1. **创建 RDS PostgreSQL 实例**
2. **创建 ElastiCache Redis 集群**
3. **构建 Docker 镜像并推送到 ECR**
4. **创建 ECS 任务定义和服务**
5. **配置 Application Load Balancer**

#### 使用 Elastic Beanstalk
```bash
# 安装 EB CLI
pip install awsebcli

# 初始化应用
eb init

# 创建环境
eb create production

# 部署
eb deploy
```

### Google Cloud Platform

#### 使用 Cloud Run
```bash
# 构建镜像
gcloud builds submit --tag gcr.io/PROJECT_ID/ai-bi-backend

# 部署到 Cloud Run
gcloud run deploy ai-bi-backend \
  --image gcr.io/PROJECT_ID/ai-bi-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Azure 部署

#### 使用 Container Instances
```bash
# 创建资源组
az group create --name ai-bi-rg --location eastus

# 部署容器
az container create \
  --resource-group ai-bi-rg \
  --name ai-bi-backend \
  --image your-registry/ai-bi-backend:latest \
  --dns-name-label ai-bi-backend \
  --ports 3001
```

## 🔒 安全配置

### 1. 环境变量安全
```bash
# 生成强密码
openssl rand -base64 32

# 设置文件权限
chmod 600 .env
chown app:app .env
```

### 2. 数据库安全
```sql
-- 限制数据库连接
ALTER SYSTEM SET listen_addresses = 'localhost';
ALTER SYSTEM SET max_connections = 100;

-- 启用 SSL
ALTER SYSTEM SET ssl = on;
```

### 3. Redis 安全
```bash
# 编辑 Redis 配置
sudo nano /etc/redis/redis.conf

# 设置密码
requirepass your_redis_password

# 绑定到本地
bind 127.0.0.1
```

### 4. Nginx 安全头
```nginx
# 安全头配置
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

## 📊 监控和日志

### 1. 应用监控
```bash
# PM2 监控
pm2 monit

# 查看日志
pm2 logs

# 重启应用
pm2 restart all
```

### 2. 系统监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 查看系统资源
htop
iotop
nethogs
```

### 3. 日志管理
```bash
# 配置 logrotate
sudo nano /etc/logrotate.d/ai-bi

# 内容
/var/log/ai-bi/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 app app
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 🔄 备份和恢复

### 数据库备份
```bash
# 创建备份
pg_dump -h localhost -U ai_bi_user ai_bi_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql -h localhost -U ai_bi_user ai_bi_system < backup_20231201_120000.sql
```

### 自动备份脚本
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/var/backups/ai-bi"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -h localhost -U ai_bi_user ai_bi_system > $BACKUP_DIR/db_$DATE.sql

# 压缩备份
gzip $BACKUP_DIR/db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 检查连接配置
psql -h localhost -U ai_bi_user -d ai_bi_system
```

2. **Redis 连接失败**
```bash
# 检查 Redis 状态
sudo systemctl status redis

# 测试连接
redis-cli ping
```

3. **端口占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :3001
sudo lsof -i :3001

# 杀死进程
sudo kill -9 <PID>
```

4. **内存不足**
```bash
# 查看内存使用
free -h

# 清理缓存
sudo sync && sudo sysctl vm.drop_caches=3
```

### 日志分析
```bash
# 查看应用日志
tail -f /var/log/ai-bi/app.log

# 查看错误日志
grep ERROR /var/log/ai-bi/app.log

# 查看系统日志
sudo journalctl -u ai-bi-backend -f
```

## 📈 性能优化

### 1. 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_data_source_type ON data_sources(type);
CREATE INDEX idx_chat_history_session ON chat_history(session_id);

-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM data_sources WHERE type = 'postgresql';
```

### 2. Redis 缓存优化
```bash
# 配置内存策略
maxmemory 256mb
maxmemory-policy allkeys-lru
```

### 3. 应用优化
```javascript
// 连接池配置
const pool = new Pool({
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

## 🔄 更新和维护

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
npm ci

# 运行迁移
npm run migrate:deploy

# 重新构建
npm run build

# 重启服务
pm2 restart all
```

### 定期维护
```bash
# 清理日志
sudo logrotate -f /etc/logrotate.d/ai-bi

# 更新系统
sudo apt update && sudo apt upgrade

# 重启服务
sudo systemctl restart postgresql redis
```
