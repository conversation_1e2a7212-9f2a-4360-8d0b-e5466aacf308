# 关联关系自动发现使用指南

## 概述

AI-BIM系统提供了强大的关联关系自动发现功能，能够根据不同数据源的结构特征自动构建关联关系。系统支持多种发现方法，并为每个发现的关联关系提供置信度评分，帮助用户判断关联关系的可靠性。

## 支持的数据源类型

### 1. 数据库类型
- **PostgreSQL**: 支持外键约束检测、命名约定匹配、数据分析
- **MySQL**: 支持外键约束检测、命名约定匹配、数据分析

### 2. 文件类型
- **CSV**: 支持命名约定匹配、数据分析
- **Excel**: 支持命名约定匹配、数据分析

## 发现方法

### 1. 外键约束检测 (foreign_key)
- **适用范围**: 仅限数据库类型
- **置信度**: 高 (0.95)
- **原理**: 从数据库的information_schema中获取外键约束信息
- **示例**: 
  ```sql
  -- PostgreSQL外键约束
  ALTER TABLE orders ADD CONSTRAINT fk_customer 
  FOREIGN KEY (customer_id) REFERENCES customers(id);
  ```

### 2. 命名约定匹配 (naming_convention)
- **适用范围**: 所有数据源类型
- **置信度**: 中-高 (0.5-0.9)
- **支持的模式**:
  - 完全匹配: `id` ↔ `id`
  - 主键-外键: `id` ↔ `customer_id`
  - 表名前缀: `customer_name` ↔ `order_customer_name`
  - 常见后缀: `_id` ↔ `_key`, `_ref`
  - 复数/单数: `customer` ↔ `customers`
  - 命名风格: `user_id` ↔ `userId`

### 3. 数据分析 (data_analysis)
- **适用范围**: 所有数据源类型
- **置信度**: 中 (0.3-0.8)
- **分析维度**:
  - 值重叠率: 两列共同值的比例
  - 唯一性分析: 列值的唯一性比例
  - 数据类型兼容性
  - 数值范围重叠

### 4. 手动创建 (manual)
- **适用范围**: 所有数据源类型
- **置信度**: 最高 (1.0)
- **说明**: 用户通过界面手动创建的关联关系

## API接口

### 1. 发现单个数据源的关联关系

```http
POST /api/semantic/data-sources/{dataSourceId}/discover-relationships
Content-Type: application/json

{
  "enableForeignKeyDetection": true,
  "enableNamingConvention": true,
  "enableDataAnalysis": true,
  "confidenceThreshold": 0.3,
  "maxSuggestions": 50,
  "autoApply": false,
  "autoApplyThreshold": 0.8
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "discoveredCount": 5,
    "appliedCount": 0,
    "relationships": [
      {
        "fromColumnId": "uuid-1",
        "toColumnId": "uuid-2",
        "relationshipType": "one_to_many",
        "discoveryMethod": "foreign_key",
        "confidence": "high",
        "confidenceScore": 0.95,
        "evidence": [
          "数据库外键约束",
          "orders.customer_id -> customers.id"
        ],
        "metadata": {
          "constraintName": "orders_customer_id_fkey",
          "sourceTable": "orders",
          "targetTable": "customers"
        }
      }
    ]
  },
  "message": "成功发现 5 个关联关系"
}
```

### 2. 发现跨数据源关联关系

```http
POST /api/semantic/data-sources/{sourceDataSourceId}/discover-cross-relationships
Content-Type: application/json

{
  "targetDataSourceId": "target-uuid", // 可选，不指定则与所有其他数据源比较
  "confidenceThreshold": 0.3,
  "maxSuggestions": 50,
  "autoApply": false,
  "autoApplyThreshold": 0.8
}
```

### 3. 批量发现所有关联关系

```http
POST /api/semantic/discover-all-relationships
Content-Type: application/json

{
  "enableForeignKeyDetection": true,
  "enableNamingConvention": true,
  "enableDataAnalysis": true,
  "enableCrossDataSource": true,
  "confidenceThreshold": 0.3,
  "maxSuggestions": 100,
  "autoApply": false,
  "autoApplyThreshold": 0.8
}
```

### 4. 应用发现的关联关系

```http
POST /api/semantic/apply-relationships
Content-Type: application/json

{
  "relationships": [
    {
      "fromColumnId": "uuid-1",
      "toColumnId": "uuid-2",
      "relationshipType": "one_to_many",
      "discoveryMethod": "naming_convention",
      "confidence": "medium",
      "confidenceScore": 0.7
    }
  ],
  "autoApplyThreshold": 0.8
}
```

## 置信度评分系统

### 置信度等级
- **High (高)**: 0.8 - 1.0
- **Medium (中)**: 0.5 - 0.8
- **Low (低)**: 0.0 - 0.5

### 评分因子

#### 外键约束关系
- 数据库外键约束: +0.8 (权重: 0.8)
- 数据类型匹配: +0.3 (权重: 0.3)
- 命名约定匹配: +0.2 (权重: 0.2)

#### 命名约定关系
- 完全匹配: +0.9 (权重: 0.8)
- 模式匹配: +0.8 (权重: 0.6)
- 模糊匹配: +0.4-0.8 (权重: 0.4)
- 数据类型匹配: +0.3 (权重: 0.3)

#### 数据分析关系
- 值重叠率: +0.1-0.9 (权重: 0.5)
- 高唯一性: +0.8 (权重: 0.3)
- 数据类型匹配: +0.2 (权重: 0.2)
- 样本大小调整: ×(样本量/100)

## 使用建议

### 1. 配置建议
- **新系统**: 启用所有发现方法，置信度阈值设为0.3
- **生产环境**: 优先使用外键约束，置信度阈值设为0.6
- **跨数据源**: 重点关注命名约定和数据分析，置信度阈值设为0.5

### 2. 最佳实践
1. **分阶段发现**: 先发现单个数据源内部关系，再发现跨数据源关系
2. **人工审核**: 对于中低置信度的关系，建议人工审核后再应用
3. **定期更新**: 数据结构变化后，重新运行关联关系发现
4. **备份验证**: 应用关联关系前，备份现有配置

### 3. 性能优化
- 大数据集建议设置较高的置信度阈值
- 限制最大建议数量避免过多结果
- 使用数据采样进行分析以提高性能

## 故障排除

### 常见问题

1. **发现的关联关系过多**
   - 提高置信度阈值
   - 减少最大建议数量
   - 禁用某些发现方法

2. **发现的关联关系过少**
   - 降低置信度阈值
   - 启用更多发现方法
   - 检查数据源连接状态

3. **跨数据源关系置信度低**
   - 检查字段命名是否一致
   - 验证数据类型兼容性
   - 确保数据值有足够重叠

4. **外键约束检测失败**
   - 确认数据库用户有足够权限
   - 检查information_schema访问权限
   - 验证数据库连接配置

### 日志分析
系统会记录详细的发现过程日志，包括：
- 发现方法执行情况
- 置信度计算过程
- 错误和警告信息
- 性能统计数据

通过分析这些日志可以帮助优化发现配置和排查问题。
