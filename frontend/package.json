{"name": "ai-bi-frontend", "version": "1.0.0", "description": "AI-BI System Frontend", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "recharts": "^2.8.0", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0", "lodash": "^4.17.21", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/prismjs": "^1.26.3", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.3.3", "vite": "^5.0.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}