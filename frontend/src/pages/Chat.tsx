import React, { useState, useRef, useEffect } from 'react';
import { useMutation, useQuery } from 'react-query';
import {
  Box,
  Paper,
  TextField,
  IconButton,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Chip,
  Divider,
} from '@mui/material';
import {
  Send as SendIcon,
  Person as PersonIcon,
  SmartToy as SmartToyIcon,
} from '@mui/icons-material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { chatApi } from '../services/api';
import { ChatResponse } from '../types';

const Chat: React.FC = () => {
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<ChatResponse[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 发送消息
  const sendMessageMutation = useMutation(chatApi.sendMessage, {
    onSuccess: (response) => {
      setMessages(prev => [...prev, response]);
      setInputValue('');
    },
    onError: (error: Error) => {
      console.error('发送消息失败:', error);
    }
  });

  // 加载聊天历史
  const { data: chatHistory } = useQuery(
    ['chatHistory', sessionId],
    () => chatApi.getChatHistory(sessionId),
    {
      enabled: !!sessionId,
      onSuccess: (data) => {
        setMessages(data.reverse()); // 反转顺序，最新的在下面
      }
    }
  );

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatResponse = {
      id: `temp_${Date.now()}`,
      sessionId,
      userPrompt: inputValue,
      responseText: '',
    };

    setMessages(prev => [...prev, userMessage]);

    sendMessageMutation.mutate({
      sessionId,
      prompt: inputValue
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const renderVisualization = (visualization: any, data: any) => {
    if (!visualization || !data) return null;

    const chartData = data.rows?.map((row: any[], index: number) => {
      const obj: any = { index };
      data.columns?.forEach((col: string, colIndex: number) => {
        obj[col] = row[colIndex];
      });
      return obj;
    }) || [];

    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

    switch (visualization.type) {
      case 'bar_chart':
        return (
          <Box sx={{ width: '100%', height: 300, mt: 2 }}>
            <ResponsiveContainer>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={data.columns?.[0]} />
                <YAxis />
                <Tooltip />
                <Bar dataKey={data.columns?.[1]} fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        );

      case 'line_chart':
        return (
          <Box sx={{ width: '100%', height: 300, mt: 2 }}>
            <ResponsiveContainer>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={data.columns?.[0]} />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey={data.columns?.[1]} stroke="#8884d8" />
              </LineChart>
            </ResponsiveContainer>
          </Box>
        );

      case 'pie_chart':
        return (
          <Box sx={{ width: '100%', height: 300, mt: 2 }}>
            <ResponsiveContainer>
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey={data.columns?.[1]}
                  nameKey={data.columns?.[0]}
                >
                  {chartData.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        );

      default:
        return null;
    }
  };

  const renderDataTable = (data: any) => {
    if (!data || !data.columns || !data.rows) return null;

    return (
      <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 400 }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              {data.columns.map((column: string, index: number) => (
                <TableCell key={index}>{column}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.rows.map((row: any[], rowIndex: number) => (
              <TableRow key={rowIndex}>
                {row.map((cell: any, cellIndex: number) => (
                  <TableCell key={cellIndex}>{cell}</TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  return (
    <Box sx={{ height: 'calc(100vh - 120px)', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h4" gutterBottom>
        智能数据分析
      </Typography>

      {/* 聊天消息区域 */}
      <Paper
        sx={{
          flexGrow: 1,
          p: 2,
          mb: 2,
          overflow: 'auto',
          backgroundColor: '#f5f5f5'
        }}
      >
        {messages.length === 0 ? (
          <Box textAlign="center" sx={{ mt: 4 }}>
            <SmartToyIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="textSecondary" gutterBottom>
              欢迎使用智能数据分析
            </Typography>
            <Typography variant="body2" color="textSecondary">
              您可以用自然语言提问，例如：
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Chip label="最近一个月的销售总额是多少？" variant="outlined" sx={{ m: 0.5 }} />
              <Chip label="哪个客户的订单金额最高？" variant="outlined" sx={{ m: 0.5 }} />
              <Chip label="各产品类别的销售占比如何？" variant="outlined" sx={{ m: 0.5 }} />
            </Box>
          </Box>
        ) : (
          <Box>
            {messages.map((message, index) => (
              <Box key={message.id || index} sx={{ mb: 3 }}>
                {/* 用户消息 */}
                <Box display="flex" alignItems="flex-start" mb={1}>
                  <PersonIcon sx={{ mr: 1, mt: 0.5, color: 'primary.main' }} />
                  <Card sx={{ maxWidth: '70%', backgroundColor: 'primary.main', color: 'white' }}>
                    <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
                      <Typography variant="body1">
                        {message.userPrompt}
                      </Typography>
                    </CardContent>
                  </Card>
                </Box>

                {/* AI回复 */}
                {message.responseText && (
                  <Box display="flex" alignItems="flex-start" ml={4}>
                    <SmartToyIcon sx={{ mr: 1, mt: 0.5, color: 'secondary.main' }} />
                    <Card sx={{ maxWidth: '90%', backgroundColor: 'white' }}>
                      <CardContent>
                        <Typography variant="body1" gutterBottom>
                          {message.responseText}
                        </Typography>

                        {/* 数据表格 */}
                        {message.data && renderDataTable(message.data)}

                        {/* 可视化图表 */}
                        {message.visualization && renderVisualization(message.visualization, message.data)}

                        {/* SQL查询 */}
                        {message.generatedSql && (
                          <Box sx={{ mt: 2 }}>
                            <Divider sx={{ mb: 1 }} />
                            <Typography variant="caption" color="textSecondary" gutterBottom>
                              生成的SQL查询：
                            </Typography>
                            <Paper sx={{ p: 1, backgroundColor: '#f5f5f5', fontFamily: 'monospace', fontSize: '0.875rem' }}>
                              {message.generatedSql}
                            </Paper>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Box>
                )}

                {/* 加载状态 */}
                {!message.responseText && sendMessageMutation.isLoading && index === messages.length - 1 && (
                  <Box display="flex" alignItems="center" ml={4} mt={1}>
                    <SmartToyIcon sx={{ mr: 1, color: 'secondary.main' }} />
                    <Card sx={{ p: 2 }}>
                      <Box display="flex" alignItems="center">
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        <Typography variant="body2" color="textSecondary">
                          正在分析您的问题...
                        </Typography>
                      </Box>
                    </Card>
                  </Box>
                )}
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Paper>

      {/* 输入区域 */}
      <Paper sx={{ p: 2 }}>
        <Box display="flex" alignItems="flex-end">
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="请输入您的问题..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={sendMessageMutation.isLoading}
            variant="outlined"
            size="small"
          />
          <IconButton
            color="primary"
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || sendMessageMutation.isLoading}
            sx={{ ml: 1 }}
          >
            <SendIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* 错误提示 */}
      {sendMessageMutation.error && (
        <Alert severity="error" sx={{ mt: 1 }}>
          发送消息失败: {(sendMessageMutation.error as Error).message}
        </Alert>
      )}
    </Box>
  );
};

export default Chat;
