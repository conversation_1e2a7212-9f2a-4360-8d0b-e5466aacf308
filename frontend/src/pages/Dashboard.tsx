import React from 'react';
import { useQuery } from 'react-query';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
} from '@mui/material';
import {
  Storage as StorageIcon,
  AccountTree as AccountTreeIcon,
  Chat as ChatIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { dataSourceApi } from '../services/api';
import { DataSourceStatus } from '../types';

const Dashboard: React.FC = () => {
  const { data: dataSources, isLoading, error } = useQuery(
    'dataSources',
    dataSourceApi.getDataSources,
    {
      refetchInterval: 30000, // 每30秒刷新一次
    }
  );

  const getStatusColor = (status: DataSourceStatus) => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'disconnected':
        return 'warning';
      case 'error':
        return 'error';
      case 'syncing':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: DataSourceStatus) => {
    switch (status) {
      case 'connected':
        return '已连接';
      case 'disconnected':
        return '未连接';
      case 'error':
        return '错误';
      case 'syncing':
        return '同步中';
      default:
        return '未知';
    }
  };

  const connectedCount = dataSources?.filter(ds => ds.status === 'connected').length || 0;
  const totalCount = dataSources?.length || 0;

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        加载仪表板数据失败: {(error as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        系统概览
      </Typography>
      
      <Grid container spacing={3}>
        {/* 统计卡片 */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <StorageIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    数据源总数
                  </Typography>
                  <Typography variant="h4">
                    {totalCount}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <TrendingUpIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    已连接数据源
                  </Typography>
                  <Typography variant="h4">
                    {connectedCount}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <AccountTreeIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    语义模型
                  </Typography>
                  <Typography variant="h4">
                    {dataSources?.reduce((acc, ds) => acc + (ds.status === 'connected' ? 1 : 0), 0) || 0}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ChatIcon color="secondary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    智能分析
                  </Typography>
                  <Typography variant="h4">
                    可用
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 数据源状态列表 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                数据源状态
              </Typography>
              {dataSources && dataSources.length > 0 ? (
                <Grid container spacing={2}>
                  {dataSources.map((dataSource) => (
                    <Grid item xs={12} sm={6} md={4} key={dataSource.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                            <Typography variant="subtitle1" noWrap>
                              {dataSource.name}
                            </Typography>
                            <Chip
                              label={getStatusText(dataSource.status)}
                              color={getStatusColor(dataSource.status)}
                              size="small"
                            />
                          </Box>
                          <Typography color="textSecondary" variant="body2">
                            类型: {dataSource.type.toUpperCase()}
                          </Typography>
                          <Typography color="textSecondary" variant="body2">
                            更新时间: {new Date(dataSource.updatedAt).toLocaleString()}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography color="textSecondary">
                  暂无数据源，请先添加数据源。
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 快速操作 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                快速开始
              </Typography>
              <Typography variant="body1" paragraph>
                欢迎使用 AI-BI 智能商业分析系统！您可以：
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" paragraph>
                  在"数据源管理"中添加和配置您的数据源
                </Typography>
                <Typography component="li" variant="body2" paragraph>
                  在"语义模型"中设置业务别名和关联关系
                </Typography>
                <Typography component="li" variant="body2" paragraph>
                  在"智能分析"中使用自然语言进行数据查询和分析
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
