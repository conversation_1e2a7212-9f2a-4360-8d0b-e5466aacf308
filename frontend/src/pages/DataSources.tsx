import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Sync as SyncIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { dataSourceApi } from '../services/api';
import { DataSourceListItem, DataSourceType, CreateDataSourceRequest, ModalState } from '../types';

const DataSources: React.FC = () => {
  const queryClient = useQueryClient();
  const [modalState, setModalState] = useState<ModalState>({ open: false, mode: 'create' });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });
  const [formData, setFormData] = useState<CreateDataSourceRequest>({
    name: '',
    type: 'postgresql',
    connectionConfig: {
      host: '',
      port: 5432,
      database: '',
      username: '',
      password: ''
    }
  });

  // 查询数据源列表
  const { data: dataSources, isLoading, error } = useQuery(
    'dataSources',
    dataSourceApi.getDataSources,
    {
      refetchInterval: 30000,
    }
  );

  // 创建数据源
  const createMutation = useMutation(dataSourceApi.createDataSource, {
    onSuccess: () => {
      queryClient.invalidateQueries('dataSources');
      setModalState({ open: false, mode: 'create' });
      setSnackbar({ open: true, message: '数据源创建成功', severity: 'success' });
      resetForm();
    },
    onError: (error: Error) => {
      setSnackbar({ open: true, message: `创建失败: ${error.message}`, severity: 'error' });
    }
  });

  // 删除数据源
  const deleteMutation = useMutation(dataSourceApi.deleteDataSource, {
    onSuccess: () => {
      queryClient.invalidateQueries('dataSources');
      setSnackbar({ open: true, message: '数据源删除成功', severity: 'success' });
    },
    onError: (error: Error) => {
      setSnackbar({ open: true, message: `删除失败: ${error.message}`, severity: 'error' });
    }
  });

  // 同步元数据
  const syncMutation = useMutation(dataSourceApi.syncMetadata, {
    onSuccess: () => {
      queryClient.invalidateQueries('dataSources');
      setSnackbar({ open: true, message: '元数据同步成功', severity: 'success' });
    },
    onError: (error: Error) => {
      setSnackbar({ open: true, message: `同步失败: ${error.message}`, severity: 'error' });
    }
  });

  const handleOpenModal = (mode: 'create' | 'edit', data?: DataSourceListItem) => {
    setModalState({ open: true, mode, data });
    if (mode === 'edit' && data) {
      setFormData({
        name: data.name,
        type: data.type,
        connectionConfig: {
          host: '',
          port: 5432,
          database: '',
          username: '',
          password: ''
        }
      });
    }
  };

  const handleCloseModal = () => {
    setModalState({ open: false, mode: 'create' });
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'postgresql',
      connectionConfig: {
        host: '',
        port: 5432,
        database: '',
        username: '',
        password: ''
      }
    });
  };

  const handleSubmit = () => {
    if (modalState.mode === 'create') {
      createMutation.mutate(formData);
    }
    // TODO: 实现编辑功能
  };

  const handleDelete = (id: string) => {
    if (window.confirm('确定要删除这个数据源吗？')) {
      deleteMutation.mutate(id);
    }
  };

  const handleSync = (id: string) => {
    syncMutation.mutate(id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'success';
      case 'disconnected': return 'warning';
      case 'error': return 'error';
      case 'syncing': return 'info';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return '已连接';
      case 'disconnected': return '未连接';
      case 'error': return '错误';
      case 'syncing': return '同步中';
      default: return '未知';
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error">
        加载数据源失败: {(error as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">数据源管理</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenModal('create')}
        >
          添加数据源
        </Button>
      </Box>

      <Grid container spacing={3}>
        {dataSources?.map((dataSource) => (
          <Grid item xs={12} sm={6} md={4} key={dataSource.id}>
            <Card>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Typography variant="h6" noWrap>
                    {dataSource.name}
                  </Typography>
                  <Chip
                    label={getStatusText(dataSource.status)}
                    color={getStatusColor(dataSource.status)}
                    size="small"
                  />
                </Box>
                
                <Typography color="textSecondary" variant="body2" gutterBottom>
                  类型: {dataSource.type.toUpperCase()}
                </Typography>
                
                <Typography color="textSecondary" variant="body2" gutterBottom>
                  创建时间: {new Date(dataSource.createdAt).toLocaleString()}
                </Typography>
                
                <Typography color="textSecondary" variant="body2" gutterBottom>
                  更新时间: {new Date(dataSource.updatedAt).toLocaleString()}
                </Typography>

                <Box display="flex" justifyContent="flex-end" mt={2}>
                  <IconButton
                    size="small"
                    onClick={() => handleSync(dataSource.id)}
                    disabled={syncMutation.isLoading}
                    title="同步元数据"
                  >
                    <SyncIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleOpenModal('edit', dataSource)}
                    title="编辑"
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDelete(dataSource.id)}
                    disabled={deleteMutation.isLoading}
                    title="删除"
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {dataSources?.length === 0 && (
        <Box textAlign="center" mt={4}>
          <Typography color="textSecondary" variant="h6">
            暂无数据源
          </Typography>
          <Typography color="textSecondary" variant="body2" paragraph>
            点击"添加数据源"按钮开始配置您的第一个数据源
          </Typography>
        </Box>
      )}

      {/* 创建/编辑数据源对话框 */}
      <Dialog open={modalState.open} onClose={handleCloseModal} maxWidth="sm" fullWidth>
        <DialogTitle>
          {modalState.mode === 'create' ? '添加数据源' : '编辑数据源'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="数据源名称"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              margin="normal"
            />
            
            <FormControl fullWidth margin="normal">
              <InputLabel>数据源类型</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as DataSourceType })}
              >
                <MenuItem value="postgresql">PostgreSQL</MenuItem>
                <MenuItem value="mysql">MySQL</MenuItem>
                <MenuItem value="csv">CSV 文件</MenuItem>
                <MenuItem value="excel">Excel 文件</MenuItem>
              </Select>
            </FormControl>

            {(formData.type === 'postgresql' || formData.type === 'mysql') && (
              <>
                <TextField
                  fullWidth
                  label="主机地址"
                  value={(formData.connectionConfig as any).host || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    connectionConfig: { ...formData.connectionConfig, host: e.target.value }
                  })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="端口"
                  type="number"
                  value={(formData.connectionConfig as any).port || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    connectionConfig: { ...formData.connectionConfig, port: parseInt(e.target.value) }
                  })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="数据库名"
                  value={(formData.connectionConfig as any).database || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    connectionConfig: { ...formData.connectionConfig, database: e.target.value }
                  })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="用户名"
                  value={(formData.connectionConfig as any).username || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    connectionConfig: { ...formData.connectionConfig, username: e.target.value }
                  })}
                  margin="normal"
                />
                <TextField
                  fullWidth
                  label="密码"
                  type="password"
                  value={(formData.connectionConfig as any).password || ''}
                  onChange={(e) => setFormData({
                    ...formData,
                    connectionConfig: { ...formData.connectionConfig, password: e.target.value }
                  })}
                  margin="normal"
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal}>取消</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={createMutation.isLoading}
          >
            {createMutation.isLoading ? <CircularProgress size={20} /> : '保存'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DataSources;
