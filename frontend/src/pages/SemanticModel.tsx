import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Chip,
  Snackbar,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Edit as EditIcon,
  Link as LinkIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material';
import { dataSourceApi, semanticApi } from '../services/api';
import { DataSourceSchema, DataTableInfo, DataColumnInfo } from '../types';

const SemanticModel: React.FC = () => {
  const queryClient = useQueryClient();
  const [selectedDataSource, setSelectedDataSource] = useState<string>('');
  const [editDialog, setEditDialog] = useState({ open: false, type: '', id: '', data: {} });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // 查询数据源列表
  const { data: dataSources } = useQuery('dataSources', dataSourceApi.getDataSources);

  // 查询选中数据源的Schema
  const { data: schema, isLoading: schemaLoading } = useQuery(
    ['dataSourceSchema', selectedDataSource],
    () => dataSourceApi.getDataSourceSchema(selectedDataSource),
    {
      enabled: !!selectedDataSource,
    }
  );

  // 更新表别名
  const updateTableAliasMutation = useMutation(
    ({ tableId, data }: { tableId: string; data: any }) => semanticApi.updateTableAlias(tableId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['dataSourceSchema', selectedDataSource]);
        setEditDialog({ open: false, type: '', id: '', data: {} });
        setSnackbar({ open: true, message: '表别名更新成功', severity: 'success' });
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  // 更新列别名
  const updateColumnAliasMutation = useMutation(
    ({ columnId, data }: { columnId: string; data: any }) => semanticApi.updateColumnAlias(columnId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['dataSourceSchema', selectedDataSource]);
        setEditDialog({ open: false, type: '', id: '', data: {} });
        setSnackbar({ open: true, message: '列别名更新成功', severity: 'success' });
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `更新失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  // 自动发现关联关系
  const discoverRelationshipsMutation = useMutation(
    semanticApi.discoverRelationships,
    {
      onSuccess: (count) => {
        queryClient.invalidateQueries(['dataSourceSchema', selectedDataSource]);
        setSnackbar({ open: true, message: `成功发现 ${count} 个关联关系`, severity: 'success' });
      },
      onError: (error: Error) => {
        setSnackbar({ open: true, message: `发现关联关系失败: ${error.message}`, severity: 'error' });
      }
    }
  );

  const handleEditTable = (table: DataTableInfo) => {
    setEditDialog({
      open: true,
      type: 'table',
      id: table.id,
      data: {
        aliasName: table.aliasName || '',
        description: table.description || ''
      }
    });
  };

  const handleEditColumn = (column: DataColumnInfo) => {
    setEditDialog({
      open: true,
      type: 'column',
      id: column.id,
      data: {
        aliasName: column.aliasName || '',
        description: column.description || ''
      }
    });
  };

  const handleSaveEdit = () => {
    if (editDialog.type === 'table') {
      updateTableAliasMutation.mutate({
        tableId: editDialog.id,
        data: editDialog.data
      });
    } else if (editDialog.type === 'column') {
      updateColumnAliasMutation.mutate({
        columnId: editDialog.id,
        data: editDialog.data
      });
    }
  };

  const handleDiscoverRelationships = () => {
    if (selectedDataSource) {
      discoverRelationshipsMutation.mutate(selectedDataSource);
    }
  };

  const connectedDataSources = dataSources?.filter(ds => ds.status === 'connected') || [];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">语义模型配置</Typography>
        {selectedDataSource && (
          <Button
            variant="outlined"
            startIcon={<AutoAwesomeIcon />}
            onClick={handleDiscoverRelationships}
            disabled={discoverRelationshipsMutation.isLoading}
          >
            自动发现关联关系
          </Button>
        )}
      </Box>

      <Grid container spacing={3}>
        {/* 数据源选择 */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                选择数据源
              </Typography>
              {connectedDataSources.map((dataSource) => (
                <Card
                  key={dataSource.id}
                  variant={selectedDataSource === dataSource.id ? "outlined" : "elevation"}
                  sx={{
                    mb: 1,
                    cursor: 'pointer',
                    bgcolor: selectedDataSource === dataSource.id ? 'action.selected' : 'background.paper'
                  }}
                  onClick={() => setSelectedDataSource(dataSource.id)}
                >
                  <CardContent sx={{ py: 1, '&:last-child': { pb: 1 } }}>
                    <Typography variant="subtitle2">
                      {dataSource.name}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {dataSource.type.toUpperCase()}
                    </Typography>
                  </CardContent>
                </Card>
              ))}
              {connectedDataSources.length === 0 && (
                <Typography color="textSecondary" variant="body2">
                  暂无已连接的数据源
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* 数据模型详情 */}
        <Grid item xs={12} md={9}>
          {!selectedDataSource ? (
            <Card>
              <CardContent>
                <Typography color="textSecondary" variant="h6" textAlign="center">
                  请选择一个数据源查看其语义模型
                </Typography>
              </CardContent>
            </Card>
          ) : schemaLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
              <CircularProgress />
            </Box>
          ) : schema ? (
            <Box>
              {/* 数据表列表 */}
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    数据表 ({schema.tables.length})
                  </Typography>
                  {schema.tables.map((table) => (
                    <Accordion key={table.id}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box display="flex" alignItems="center" width="100%">
                          <Box flexGrow={1}>
                            <Typography variant="subtitle1">
                              {table.aliasName || table.originalName}
                              {table.aliasName && (
                                <Typography component="span" variant="caption" color="textSecondary" sx={{ ml: 1 }}>
                                  ({table.originalName})
                                </Typography>
                              )}
                            </Typography>
                            {table.description && (
                              <Typography variant="body2" color="textSecondary">
                                {table.description}
                              </Typography>
                            )}
                          </Box>
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditTable(table);
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <TableContainer component={Paper} variant="outlined">
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <TableCell>列名</TableCell>
                                <TableCell>别名</TableCell>
                                <TableCell>数据类型</TableCell>
                                <TableCell>描述</TableCell>
                                <TableCell>操作</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {table.columns.map((column) => (
                                <TableRow key={column.id}>
                                  <TableCell>
                                    {column.originalName}
                                    {column.isPrimaryKey && (
                                      <Chip label="PK" size="small" color="primary" sx={{ ml: 1 }} />
                                    )}
                                  </TableCell>
                                  <TableCell>{column.aliasName || '-'}</TableCell>
                                  <TableCell>{column.dataType}</TableCell>
                                  <TableCell>{column.description || '-'}</TableCell>
                                  <TableCell>
                                    <IconButton
                                      size="small"
                                      onClick={() => handleEditColumn(column)}
                                    >
                                      <EditIcon />
                                    </IconButton>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </CardContent>
              </Card>

              {/* 关联关系 */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    关联关系 ({schema.relationships.length})
                  </Typography>
                  {schema.relationships.length > 0 ? (
                    <TableContainer component={Paper} variant="outlined">
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>起始表.列</TableCell>
                            <TableCell>目标表.列</TableCell>
                            <TableCell>关系类型</TableCell>
                            <TableCell>创建方式</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {schema.relationships.map((relationship) => (
                            <TableRow key={relationship.id}>
                              <TableCell>
                                {/* 这里需要根据columnId查找对应的表和列名 */}
                                起始列
                              </TableCell>
                              <TableCell>
                                目标列
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={relationship.type}
                                  size="small"
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={relationship.isManual ? '手动' : '自动'}
                                  size="small"
                                  color={relationship.isManual ? 'primary' : 'default'}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  ) : (
                    <Typography color="textSecondary">
                      暂无关联关系，点击"自动发现关联关系"按钮进行自动发现
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Box>
          ) : (
            <Alert severity="error">
              加载数据模型失败
            </Alert>
          )}
        </Grid>
      </Grid>

      {/* 编辑对话框 */}
      <Dialog open={editDialog.open} onClose={() => setEditDialog({ open: false, type: '', id: '', data: {} })} maxWidth="sm" fullWidth>
        <DialogTitle>
          编辑{editDialog.type === 'table' ? '表' : '列'}别名
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="别名"
            value={(editDialog.data as any).aliasName || ''}
            onChange={(e) => setEditDialog({
              ...editDialog,
              data: { ...editDialog.data, aliasName: e.target.value }
            })}
            margin="normal"
          />
          <TextField
            fullWidth
            label="描述"
            multiline
            rows={3}
            value={(editDialog.data as any).description || ''}
            onChange={(e) => setEditDialog({
              ...editDialog,
              data: { ...editDialog.data, description: e.target.value }
            })}
            margin="normal"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog({ open: false, type: '', id: '', data: {} })}>
            取消
          </Button>
          <Button
            onClick={handleSaveEdit}
            variant="contained"
            disabled={updateTableAliasMutation.isLoading || updateColumnAliasMutation.isLoading}
          >
            保存
          </Button>
        </DialogActions>
      </Dialog>

      {/* 消息提示 */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SemanticModel;
