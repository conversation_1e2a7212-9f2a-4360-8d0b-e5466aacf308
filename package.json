{"name": "ai-bi-system", "version": "1.0.0", "description": "AI-Powered Business Intelligence System", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["ai", "bi", "business-intelligence", "nlp", "data-analysis"], "author": "AI-BI Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend"]}