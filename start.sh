#!/bin/bash

# AI-BI 系统启动脚本
# 用于快速启动开发环境

set -e

echo "🚀 AI-BI 智能商业分析系统启动脚本"
echo "=================================="

# 检查 Node.js 版本
check_node() {
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo "❌ Node.js 版本过低，需要 18+，当前版本: $(node -v)"
        exit 1
    fi
    
    echo "✅ Node.js 版本检查通过: $(node -v)"
}

# 检查 Docker
check_docker() {
    if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
        echo "✅ Docker 环境可用"
        return 0
    else
        echo "⚠️  Docker 未安装，将使用本地开发模式"
        return 1
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        echo "❌ 未找到 package.json，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    npm install
    
    echo "📦 安装后端依赖..."
    cd backend && npm install && cd ..
    
    echo "📦 安装前端依赖..."
    cd frontend && npm install && cd ..
    
    echo "✅ 依赖安装完成"
}

# 设置环境变量
setup_env() {
    echo "⚙️  设置环境变量..."
    
    # 后端环境变量
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        echo "📝 已创建 backend/.env，请根据需要修改配置"
    fi
    
    # 前端环境变量
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        echo "📝 已创建 frontend/.env，请根据需要修改配置"
    fi
    
    echo "✅ 环境变量设置完成"
}

# Docker 模式启动
start_with_docker() {
    echo "🐳 使用 Docker 启动服务..."
    
    # 检查是否存在 docker-compose.yml
    if [ ! -f "docker-compose.yml" ]; then
        echo "❌ 未找到 docker-compose.yml 文件"
        exit 1
    fi
    
    # 启动服务
    docker-compose up -d
    
    echo "⏳ 等待服务启动..."
    sleep 10
    
    # 检查服务状态
    echo "📊 服务状态:"
    docker-compose ps
    
    echo ""
    echo "🎉 服务启动完成！"
    echo "📱 前端应用: http://localhost:3000"
    echo "🔧 后端API: http://localhost:3001"
    echo "🗄️  数据库: localhost:5432"
    echo "🔴 Redis: localhost:6379"
    echo ""
    echo "📋 查看日志: docker-compose logs -f"
    echo "🛑 停止服务: docker-compose down"
}

# 本地模式启动
start_local() {
    echo "💻 使用本地模式启动服务..."
    
    # 检查数据库连接
    echo "🔍 检查数据库连接..."
    if ! command -v psql &> /dev/null; then
        echo "⚠️  PostgreSQL 客户端未安装，请确保数据库已正确配置"
    fi
    
    # 生成 Prisma 客户端
    echo "🔧 生成 Prisma 客户端..."
    cd backend && npm run db:generate && cd ..
    
    # 运行数据库迁移
    echo "🗄️  运行数据库迁移..."
    cd backend && npm run migrate && cd ..
    
    # 构建后端
    echo "🔨 构建后端..."
    cd backend && npm run build && cd ..
    
    # 启动服务
    echo "🚀 启动服务..."
    
    # 使用 concurrently 同时启动前后端
    if command -v concurrently &> /dev/null; then
        npm run dev
    else
        echo "📦 安装 concurrently..."
        npm install -g concurrently
        npm run dev
    fi
}

# 主菜单
show_menu() {
    echo ""
    echo "请选择启动模式:"
    echo "1) Docker 模式 (推荐)"
    echo "2) 本地开发模式"
    echo "3) 仅安装依赖"
    echo "4) 退出"
    echo ""
    read -p "请输入选项 (1-4): " choice
    
    case $choice in
        1)
            if check_docker; then
                start_with_docker
            else
                echo "❌ Docker 不可用，请选择其他模式"
                show_menu
            fi
            ;;
        2)
            start_local
            ;;
        3)
            install_dependencies
            setup_env
            echo "✅ 依赖安装完成，请手动启动服务"
            ;;
        4)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            show_menu
            ;;
    esac
}

# 主函数
main() {
    # 检查基础环境
    check_node
    
    # 如果是首次运行，安装依赖
    if [ ! -d "node_modules" ] || [ ! -d "backend/node_modules" ] || [ ! -d "frontend/node_modules" ]; then
        echo "🔍 检测到首次运行，正在安装依赖..."
        install_dependencies
        setup_env
    fi
    
    # 显示菜单
    show_menu
}

# 处理中断信号
trap 'echo ""; echo "👋 启动脚本已停止"; exit 0' INT

# 运行主函数
main
