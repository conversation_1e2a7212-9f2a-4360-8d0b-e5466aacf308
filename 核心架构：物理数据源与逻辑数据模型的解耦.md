# 核心架构：物理数据源与逻辑数据模型的解耦

## 1. 引言

本文档旨在阐述本 AI-BI 系统的一项核心架构原则：**通过中心化元数据管理和基于元数据的关联关系，实现物理数据源与逻辑数据模型的完全解耦**。

理解这一概念对于掌握系统如何灵活、高效地处理来自异构数据源的数据至关重要。

## 2. 核心概念定义

- **物理数据源 (Physical Data Source)**: 指代数据的实际存储位置和具体实现。它可以是一个 PostgreSQL 数据库、一个 MySQL 服务器、一个 CSV 文件、或是一个 Excel 表格。它们各自拥有独立的连接方式、物理表结构和查询语言。

- **逻辑数据模型 (Logical Data Model)**: 这是一个抽象层，是系统和 AI 理解业务的统一视图。它由一系列业务对象（如“客户”、“订单”）、它们的属性以及它们之间的业务关系构成。这个模型对用户和 AI 隐藏了底层数据的物理复杂性。

## 3. 解耦策略详解

系统的解耦策略主要依赖以下两个机制：

### 3.1. 中心化元数据管理

这是实现解耦的第一步。系统并不直接操作物理数据源，而是执行以下流程：

1.  **抽取 (Extraction)**: 系统连接到用户配置的各个物理数据源（如 PostgreSQL, CSV）。
2.  **注册 (Registration)**: 系统读取这些数据源的“元数据”——即关于数据的数据，例如表名、列名、数据类型等信息。
3.  **中心化存储 (Centralized Storage)**: 系统将这些来自不同源头的元数据，存储在自己内部的数据库表中（`data_tables`, `data_columns`）。在这个过程中，系统为每一个表和列都分配了一个全局唯一的标识符（UUID）。

**这个过程的意义在于**：无论原始数据存储在哪里、叫什么名字，到了系统内部，它们都被转换成了一个统一的、标准化的元数据记录。系统内部的所有操作，都将基于这些标准化的记录，而不是直接依赖于物理源。

### 3.2. 基于元数据的关联关系

这是实现解耦的关键一步，它让跨数据源分析成为可能：

1.  **定义关系**: 用户或系统可以在这个逻辑数据模型上定义业务关系。例如，用户可以指出“订单表”中的 `customer_id` 列关联到了“客户表”中的 `id` 列。
2.  **基于 UUID 链接**: 这个关联关系是记录在 `data_relationships` 表中的。它连接的不是物理表和物理列，而是那两个列在我们系统中注册后生成的**唯一标识符 (UUID)**。
3.  **跨越物理边界**: 由于这个关联是基于系统内部的、全局唯一的 UUID，所以系统完全不在乎这两个列原本是否在同一个数据库、同一个服务器、甚至是同一种数据源类型中。我们可以轻易地将一个 PostgreSQL 数据库中的“订单”数据，与一个 Excel 文件中的“区域经理”数据关联起来。

## 4. 解耦带来的核心优势

将物理层与逻辑层解耦，带来了巨大的灵活性和扩展性：

- **位置透明性 (Location Transparency)**:
  - **场景**: 公司的“用户数据库”从本地机房的 MySQL 迁移到了云上的 PostgreSQL。
  - **传统方式**: 所有相关的代码、报表、查询逻辑都需要重写。
  - **本系统**: 只需在 `data_sources` 表中更新该数据源的连接信息和类型。由于逻辑模型（别名、关系）没有改变，所有上层的 AI 查询和分析能力**完全不受影响**，无需任何修改。

- **结构变化的隔离 (Resilience to Schema Changes)**:
  - **场景**: 数据库管理员将 `users` 表中的 `name` 列重命名为 `user_name`。
  - **传统方式**: 会导致依赖 `name` 字段的查询全部失败。
  - **本系统**: 只需在 `data_columns` 元数据表中，将 `original_column_name` 从 `name` 更新为 `user_name` 即可。而该列的别名（例如“客户姓名”）和它的 UUID 保持不变，逻辑模型依然稳定，AI 查询不受影响。

- **无缝的扩展能力 (Seamless Scalability)**:
  - **场景**: 公司希望引入一个新的数据源，比如一个包含销售指标的 Salesforce 系统。
  - **传统方式**: 需要复杂的 ETL 过程和数据仓库重构。
  - **本系统**: 只需在系统中添加一个新的数据源，同步其元数据，然后将其中的关键字段（如 `user_email`）与现有逻辑模型中的字段（如客户表的 `email`）建立关联即可。新的数据源几乎可以“即插即用”地融入到现有的分析体系中。

## 5. 总结

通过将数据源的物理实现细节（“在哪里，是什么样的”）抽象为统一的逻辑业务模型（“是什么，有什么关系”），本系统成功地将 AI 和最终用户与底层数据管理的复杂性隔离开来。

这种解耦是系统能够提供强大、灵活、跨数据源自然语言查询能力的基石。
