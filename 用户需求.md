# AI-Powered Business Intelligence (BI) System: Requirements

## 1. 引言

本文档旨在详细阐述一个基于人工智能（AI）的商业智能（BI）数据管理系统的功能性及非功能性需求。该系统致力于通过利用包括自然语言处理（NLP）在内的人工智能技术，简化数据集成、分析与可视化，提供更直观、高效的用户体验。

## 2. 核心功能模块

### 2.1. 数据源管理

- **[F-DM-01] 多数据源连接**
  - **描述:** 系统需支持连接并集成多种类型的数据源，包括关系型数据库、CSV 文件和 Excel 文件。
  - **用例:**
    - **数据库:** 用户小明希望分析存储在 PostgreSQL 数据库中的销售数据。他可以在系统中配置数据库连接信息（地址、端口、用户名、密码），系统即可连接到该数据库。
    - **CSV 文件:** 市场部分析师小红有一份本地的 CSV 文件，其中包含用户调研数据。她可以将该 CSV 文件上传到系统中，系统会解析文件内容并将其作为一个数据源。
    - **Excel 文件:** 财务部分析师小李需要分析一个包含月度预算的 Excel 文件。他可以将该 Excel 文件上传，系统能够识别并加载其中的工作表。

- **[F-DM-02] 数据源状态监控**
  - **描述:** 系统应在界面上清晰地展示所有已连接数据源的列表及其连接状态（如：已连接、连接失败、同步中）。
  - **用例:** 用户可以一目了然地看到生产数据库当前是否连接正常，或者某个 CSV 文件是否已经成功导入。

### 2.2. 语义层配置

- **[F-SL-01] 自动关联关系识别**
  - **描述:** 系统应能根据数据库中的外键约束，自动识别并建议表之间的关联关系（一对一、一对多、多对多）。
  - **用例:** 当用户连接到一个包含 `orders` (订单) 表和 `customers` (客户) 表的数据库时，如果 `orders` 表有一个外键 `customer_id` 指向 `customers` 表的 `id`，系统应自动识别出 `customers` 和 `orders` 之间的一对多关系。

- **[F-SL-02] 可视化关联关系管理**
  - **描述:** 用户可以通过图形化界面（如拖拽）来手动创建、修改或删除数据表之间的关联关系，包括在不同数据源之间建立关联。
  - **用例:**
    - **手动覆盖:** 系统自动将 `users` 表和 `logs` 表通过 `user_id` 关联，但分析师希望通过 `email` 字段进行关联。此时，他可以在图形界面中删除自动创建的关联，并从 `users` 表的 `email` 列拖拽一条线到 `logs` 表的 `email` 列来新建关联。
    - **跨数据源:** 公司的产品信息存储在 MySQL 数据库中，而销售记录则在一个 CSV 文件里。用户可以在图形界面中，将 MySQL 中的 `products` 表和 CSV 文件中的销售数据通过 `product_id` 字段手动关联起来，以进行联合分析。

- **[F-SL-03] 业务别名配置**
  - **描述:** 用户可以为数据表、字段和关联关系设置易于理解的业务别名，以便于使用自然语言进行提问。
  - **用例:** 将数据库表 `dw_mart_sales_detail` 命名为“销售明细表”，将字段 `customer_name` 命名为“顾客姓名”，将 `product_id` 命名为“产品ID”。这样，用户就可以直接提问“统计一下‘顾客姓名’为张三的‘销售明细表’记录”。

### 2.3. 自然语言交互与分析

- **[F-NLA-01] 对话式查询界面**
  - **描述:** 系统提供一个类似聊天机器人的界面，用户可以通过输入自然语言文本来进行数据查询。
  - **用例:** 用户在输入框中输入“最近一个月的销售总额是多少？”，系统开始处理该问题。

- **[F-NLA-02] 自然语言到数据查询的转换**
  - **描述:** 系统内置的大语言模型（LLM）负责解析用户的自然语言问题，并将其转换为相应的查询语句（如 SQL）。
  - **用例:** 对于问题“查找所有北京地区用户的订单”，LLM 会将其转换为类似 `SELECT * FROM orders o JOIN users u ON o.user_id = u.id WHERE u.city = '北京'` 的 SQL 查询。

- **[F-NLA-03] 跨数据源查询**
  - **描述:** 系统能够处理跨越多个已关联数据源的复杂查询。
  - **用例:** 用户提问“查询一下，购买了‘AI-BIM 学习版’产品的用户的地区分布情况”，其中产品信息在 MySQL 中，用户信息在 PostgreSQL 中。系统需要执行跨数据库的联合查询来回答该问题。

- **[F-NLA-04] 自然语言生成回答**
  - **描述:** 系统将查询得到的数据结果，以自然语言的形式进行总结和呈现。
  - **用例:** 对于“最近一个月的销售总额”的查询，系统不仅返回数字，还会回答：“最近一个月的总销售额为 1,000,000 元。”

- **[F-NLA-05] 智能数据可视化**
  - **描述:** 系统根据查询结果和问题类型，自动生成最合适的数据可视化图表（如柱状图、折线图、饼图等）。
  - **用例:**
    - 当用户询问“各产品类别的销售额占比”时，系统除了给出文字回答，还会自动生成一个饼图来直观展示各类别销售额的比例。
    - 当用户询问“过去一年的每月销售额趋势”时，系统会自动生成一个折线图。

### 2.4. 前端交互与可视化

- **[F-FE-01] 数据源管理界面**
  - **描述:** 提供一个清晰的界面，用于展示、添加、编辑和删除数据源。
  - **功能:**
    - 显示所有已配置的数据源列表，包含名称、类型和连接状态。
    - 提供表单让用户输入新数据源的连接信息（如主机、端口、用户名、密码、数据库名等）。
    - 支持对现有数据源的连接信息进行修改。
    - 提供删除数据源的功能，并进行必要的风险提示。

- **[F-FE-02] 语义模型配置界面**
  - **描述:** 提供一个可视化的界面，用于管理数据模型（表、列）的元数据和它们之间的关联关系。
  - **功能:**
    - 以列表或卡片形式展示选定数据源中的所有数据表。
    - 允许用户为表和列设置或修改业务别名和描述。
    - 提供一个图形化的画布，用户可以通过拖拽连接点的方式，在不同的表和列之间建立或删除关联关系。
    - 清晰地展示已建立的关联关系，并允许用户查看和编辑关系类型（一对一、一对多等）。

- **[F-FE-03] 对话式分析主界面**
  - **描述:** 用户与 AI进行交互的核心界面，采用对话形式，支持多轮对话和上下文理解。
  - **功能:**
    - 提供一个文本输入框，供用户输入自然语言问题。
    - 聊天记录区，展示用户提问和 AI 的回答历史。
    - 支持展示多种类型的回复，包括：
      - **文本回答:** 对问题的直接文字总结。
      - **数据表格:** 结构化的查询结果数据。
      - **可视化图表:** 根据数据自动生成的图表（如柱状图、折线图、饼图等）。

- **[F-FE-04] 可视化图表交互**
  - **描述:** 生成的图表应具备基本的交互能力，以增强数据探索体验。
  - **功能:**
    - **悬停提示 (Tooltip):** 当鼠标悬停在图表的某个数据点上时，显示详细的数值和信息。
    - **图表类型切换:** 用户可以手动切换推荐的图表类型，例如从柱状图切换到折线图。
    - **数据钻取 (Drill Down):** (可选高级功能) 支持在图表上进行下钻操作，以探索更深层次的数据。
    - **图表导出:** 支持将生成的图表导出为图片（如 PNG, JPG）或数据文件（如 CSV）。

## 3. 技术栈

- **[T-BE-01] 后端:** 根据不同的模块选择合适的开发语言和框架进行开发。
- **[T-FE-01] 前端:** 使用 TypeScript 语言开发，并采用 React 或 Vue.js 等现代前端框架。

## 4. 非功能性需求

- **[NF-SEC-01] 安全性**
  - **描述:** 系统需要提供安全的数据源凭证管理机制，例如对密码等敏感信息进行加密存储。
  - **用例:** 管理员在配置数据库连接时，输入的密码在保存到系统时必须被加密，不能以明文形式存储。

- **[NF-PER-01] 性能**
  - **描述:** 系统需要能够高效处理大规模数据集和高并发的用户查询请求。
  - **用例:** 当多个用户同时使用自然语言查询功能时，系统应能在合理的时间内（例如，几秒内）返回所有用户的查询结果。

- **[NF-USA-01] 易用性**
  - **描述:** 用户界面应设计得直观简洁，使得非技术背景的业务人员也能轻松上手，无需大量培训。
  - **用例:** 业务分析师小王不了解 SQL，但他可以通过在聊天框中输入“帮我看看上个季度华东区的销售冠军是谁”，就能快速得到需要的数据和分析结果。