# 系统设计 - 数据模型

## 1. 引言

本文档详细定义了 AI-BI 系统的核心数据模型，旨在为后续的开发工作提供清晰、统一的结构指导。内容涵盖数据库表结构、缓存策略以及前后端数据传输协议（DTO）。

## 2. 数据库表结构设计 (Database Schema)

系统将使用一个主数据库（例如 PostgreSQL）来存储自身的元数据和配置信息。

### 2.1. `data_sources` - 数据源表

存储用户连接的外部数据源信息。

| 字段名              | 数据类型      | 是否可空 | 主/外键 | 描述                                                         |
| ------------------- | ------------- | -------- | ------- | ------------------------------------------------------------ |
| `id`                | `UUID`        | 否       | PK      | 唯一标识符                                                   |
| `name`              | `VARCHAR(255)`| 否       |         | 用户为数据源指定的名称，例如“生产数据库”                     |
| `type`              | `VARCHAR(50)` | 否       |         | 数据源类型 (e.g., `postgresql`, `mysql`, `csv`, `excel`)     |
| `connection_config` | `TEXT`        | 否       |         | 加密后的连接配置信息 (JSON 格式)，包含 host, port, user, password, dbname 等 |
| `created_at`        | `TIMESTAMPZ`  | 否       |         | 创建时间                                                     |
| `updated_at`        | `TIMESTAMPZ`  | 否       |         | 最后更新时间                                                 |

### 2.2. `data_tables` - 数据表元数据表

缓存并管理从外部数据源同步过来的表的元数据。

| 字段名                | 数据类型       | 是否可空 | 主/外键 | 描述                                       |
| --------------------- | -------------- | -------- | ------- | ------------------------------------------ |
| `id`                  | `UUID`         | 否       | PK      | 唯一标识符                                 |
| `data_source_id`      | `UUID`         | 否       | FK      | 关联到 `data_sources.id`                   |
| `original_table_name` | `VARCHAR(255)` | 否       |         | 在原始数据源中的表名                       |
| `alias_name`          | `VARCHAR(255)` | 是       |         | 用户定义的业务别名，例如“销售明细”         |
| `description`         | `TEXT`         | 是       |         | 用户对该表的描述                           |
| `is_synced`           | `BOOLEAN`      | 否       |         | 标记该表的元数据是否与源数据保持同步       |
| `created_at`          | `TIMESTAMPZ`   | 否       |         | 创建时间                                   |
| `updated_at`          | `TIMESTAMPZ`   | 否       |         | 最后更新时间                               |

### 2.3. `data_columns` - 数据列元数据表

缓存并管理数据表的列（字段）的元数据。

| 字段名                 | 数据类型       | 是否可空 | 主/外键 | 描述                                       |
| ---------------------- | -------------- | -------- | ------- | ------------------------------------------ |
| `id`                   | `UUID`         | 否       | PK      | 唯一标识符                                 |
| `data_table_id`        | `UUID`         | 否       | FK      | 关联到 `data_tables.id`                    |
| `original_column_name` | `VARCHAR(255)` | 否       |         | 在原始数据源中的列名                       |
| `original_data_type`   | `VARCHAR(100)` | 否       |         | 在原始数据源中的数据类型                   |
| `alias_name`           | `VARCHAR(255)` | 是       |         | 用户定义的业务别名，例如“顾客姓名”         |
| `description`          | `TEXT`         | 是       |         | 用户对该列的描述                           |
| `is_primary_key`       | `BOOLEAN`      | 否       |         | 是否是主键                                 |
| `created_at`           | `TIMESTAMPZ`   | 否       |         | 创建时间                                   |
| `updated_at`           | `TIMESTAMPZ`   | 否       |         | 最后更新时间                               |
s
### 2.4. `data_relationships` - 数据关联关系表

存储表之间的关联关系，由系统自动发现或用户手动创建。

| 字段名              | 数据类型       | 是否可空 | 主/外键 | 描述                                                         |
| ------------------- | -------------- | -------- | ------- | ------------------------------------------------------------ |
| `id`                | `UUID`         | 否       | PK      | 唯一标识符                                                   |
| `from_column_id`    | `UUID`         | 否       | FK      | 起始列，关联到 `data_columns.id`                             |
| `to_column_id`      | `UUID`         | 否       | FK      | 目标列，关联到 `data_columns.id`                             |
| `relationship_type` | `VARCHAR(50)`  | 否       |         | 关联类型 (`one_to_one`, `one_to_many`, `many_to_one`)        |
| `is_manual`         | `BOOLEAN`      | 否       |         | 是否为用户手动创建 (true) 或系统自动发现 (false)             |
| `created_at`        | `TIMESTAMPZ`   | 否       |         | 创建时间                                                     |

### 2.5. `chat_history` - 对话历史记录表

存储用户与系统的自然语言交互记录。

| 字段名                      | 数据类型     | 是否可空 | 主/外键 | 描述                                                         |
| --------------------------- | ------------ | -------- | ------- | ------------------------------------------------------------ |
| `id`                        | `UUID`       | 否       | PK      | 唯一标识符                                                   |
| `session_id`                | `VARCHAR(255)`| 否       |         | 对话会话 ID，用于聚合一次完整的对话                        |
| `user_query`                | `TEXT`       | 否       |         | 用户输入的原始自然语言问题                                 |
| `generated_sql`             | `TEXT`       | 是       |         | LLM 生成的 SQL 查询语句                                      |
| `query_result_data`         | `JSONB`      | 是       |         | 原始查询结果的数据 (JSON 格式)                               |
| `natural_language_response` | `TEXT`       | 是       |         | LLM 生成的自然语言回答                                       |
| `visualization_spec`        | `JSONB`      | 是       |         | 用于前端渲染的可视化图表配置 (e.g., Vega-Lite spec)          |
| `created_at`                | `TIMESTAMPZ` | 否       |         | 创建时间                                                     |

## 3. 缓存结构设计 (Caching)

为了提升性能和降低对外部数据源的请求压力，系统将采用多级缓存策略，推荐使用 Redis。

- **缓存 1: 数据源的 Schema 元数据**
  - **目的:** 避免频繁查询外部数据源的表和列信息。
  - **Key:** `schema:<data_source_id>`
  - **Value:** 一个 JSON 对象，包含该数据源下所有表、列及其关系的完整信息。
  - **更新时机:** 当用户手动触发同步或定期任务执行时，更新缓存。

- **缓存 2: 语义层上下文**
  - **目的:** 为 LLM 提供快速、完整的上下文信息（包含所有别名和关联关系）。
  - **Key:** `semantic_layer:context`
  - **Value:** 一个预先构建好的文本或 JSON，描述了所有已配置的业务对象、别名和它们之间的关系，直接注入到 LLM 的 prompt 中。
  - **更新时机:** 当任何 `data_tables`, `data_columns`, `data_relationships` 中的别名或关系发生变更时，立即失效并重建。

- **缓存 3: 查询结果**
  - **目的:** 缓存相同自然语言问题的查询结果，实现秒级响应。
  - **Key:** `query_result:<hash_of_user_query_and_semantic_context>`
  - **Value:** 完整的 API 响应体（包含自然语言回答、数据和可视化配置）。
  - **更新时机:** 设置较短的过期时间（例如 10-30 分钟），或在相关数据源更新后失效。

## 4. 前后端数据传输结构 (DTO)

API 将使用 JSON 格式进行通信。以下是核心接口的数据结构定义。

### 4.1. 获取数据源列表

`GET /api/data-sources`
```json
[
  {
    "id": "uuid-datasource-1",
    "name": "生产数据库",
    "type": "postgresql",
    "status": "connected"
  },
  {
    "id": "uuid-datasource-2",
    "name": "用户调研数据",
    "type": "csv",
    "status": "available"
  }
]
```

### 4.2. 获取单个数据源的 Schema 详情

`GET /api/data-sources/{id}/schema`
```json
{
  "id": "uuid-datasource-1",
  "name": "生产数据库",
  "tables": [
    {
      "id": "uuid-table-101",
      "original_name": "customers",
      "alias_name": "客户",
      "columns": [
        {
          "id": "uuid-column-1001",
          "original_name": "id",
          "alias_name": "客户ID",
          "data_type": "integer"
        },
        {
          "id": "uuid-column-1002",
          "original_name": "customer_name",
          "alias_name": "客户姓名",
          "data_type": "varchar"
        }
      ]
    }
  ],
  "relationships": [
    {
      "from_column_id": "uuid-column-1001",
      "to_column_id": "uuid-column-2005",
      "type": "one_to_many"
    }
  ]
}
```

### 4.3. 发起自然语言查询

`POST /api/chat`
```json
{
  "sessionId": "session-xyz-123",
  "prompt": "最近一个月销售额最高的客户是谁？"
}
```

### 4.4. 自然语言查询的响应

`Response from POST /api/chat`
```json
{
  "id": "chat-abc-456",
  "sessionId": "session-xyz-123",
  "user_prompt": "最近一个月销售额最高的客户是谁？",
  "response_text": "最近一个月销售额最高的客户是'张三'，总消费金额为 5,800 元。",
  "data": {
    "columns": ["客户姓名", "总销售额"],
    "rows": [
      ["张三", 5800],
      ["李四", 4200]
    ]
  },
  "visualization": {
    "type": "bar_chart",
    "title": "销售额最高的客户",
    "spec": {
      "data": {
        "values": [
          {"客户姓名": "张三", "总销售额": 5800},
          {"客户姓名": "李四", "总销售额": 4200}
        ]
      },
      "mark": "bar",
      "encoding": {
        "x": {"field": "客户姓名", "type": "nominal"},
        "y": {"field": "总销售额", "type": "quantitative"}
      }
    }
  }
}
```
